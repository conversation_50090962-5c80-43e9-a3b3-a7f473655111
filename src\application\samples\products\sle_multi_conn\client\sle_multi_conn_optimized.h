/**
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2025. All rights reserved.
 *
 * Description: SLE MULTI_CONN optimized client header. \n
 *
 * History: \n
 * 2024-01-01, Create file for 16 connections optimization. \n
 */

#ifndef SLE_MULTI_CONN_OPTIMIZED_H
#define SLE_MULTI_CONN_OPTIMIZED_H

#include "sle_ssap_client.h"
#include "sle_connection_manager.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cplusplus */
#endif /* __cplusplus */

#define SLE_MULTI_CONN_MAX_CONNECTIONS 16
#define SLE_MULTI_CONN_INVALID_INDEX 0xFF

/* 连接状态位图，节省内存 */
typedef struct {
    uint16_t connected_bitmap;          // 16位位图表示连接状态
    uint16_t param_update_bitmap;       // 16位位图表示参数更新状态
    uint8_t connected_count;            // 当前连接数量
    uint8_t scanning_index;             // 当前扫描的服务器索引
} sle_multi_conn_status_t;

/* 连接信息结构体，优化内存布局 */
typedef struct {
    uint16_t conn_id;                   // 连接ID
    uint16_t conn_hdl;                  // 连接句柄
    sle_addr_t server_addr;             // 服务器地址
    sle_uuid_t uuid;                    // UUID
} sle_connection_info_t;

/* 优化的多连接管理结构 */
typedef struct {
    sle_multi_conn_status_t status;
    sle_connection_info_t connections[SLE_MULTI_CONN_MAX_CONNECTIONS];
    ssapc_find_service_result_t services[SLE_MULTI_CONN_MAX_CONNECTIONS];
} sle_multi_conn_optimized_t;

/* 连接管理优化函数 */
uint8_t sle_multi_conn_find_free_slot(void);
uint8_t sle_multi_conn_find_by_addr(const uint8_t *addr);
uint8_t sle_multi_conn_find_by_conn_id(uint16_t conn_id);
void sle_multi_conn_set_connected(uint8_t index, bool connected);
bool sle_multi_conn_is_connected(uint8_t index);
void sle_multi_conn_set_param_updated(uint8_t index, bool updated);
bool sle_multi_conn_is_param_updated(uint8_t index);

/* 性能优化函数 */
errcode_t sle_multi_conn_batch_connect(uint8_t start_index, uint8_t count);
errcode_t sle_multi_conn_priority_connect(uint8_t *priority_list, uint8_t count);
void sle_multi_conn_load_balance_scan(void);

#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cplusplus */
#endif /* __cplusplus */

#endif /* SLE_MULTI_CONN_OPTIMIZED_H */
