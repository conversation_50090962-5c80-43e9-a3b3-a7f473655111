{"Partition_Tbl": {"image_id": "0x4B87A52D", "stru_ver": "0x00010000", "version": "0x00000000", "param_info_description": ["Address Constraints: flash type must fill with relative address, RAM regions must fill with absolute address.", "Item ID Constraints: ssb type:                       0x00 ~ 0x0F, Relative address, Size", "                     root public key and signature:  0x10 ~ 0x1F, Relative address, Size", "                     firmware on flash:              0x20 ~ 0x2F, Relative address, Size", "                     data on flash:                  0x30 ~ 0x3F, Relative address, Size", "Fixed item id:  0x00: flashboot", "                0x01: flashboot_backup", "                0x23: acpu_image", "                0x25: nv data", "                0x26: fota data"], "param_info": [["0x00", "0x00001000", "0x0000A000"], ["0x01", "0x0000B000", "0x0000A000"], ["0x23", "0x00015000", "0x0008C000"], ["0x25", "0x000FE000", "0x00002000"], ["0x26", "0x000A1000", "0x0005D000"], ["0x27", "0x00000000", "0x00000000"], ["0x28", "0x00000000", "0x00000000"], ["0x29", "0x00000000", "0x00000000"], ["0x30", "0x00000000", "0x00000000"], ["0x31", "0x00000000", "0x00000000"], ["0x32", "0x00000000", "0x00000000"], ["0x33", "0x00000000", "0x00000000"]]}, "Output_file_prefix": "AIoT", "Use_defines": true, "Partition_defines": [["0x00", "FLASHBOOT_A_IMAGE_OFFSET", "FLASHBOOT_A_IMAGE_LEN"], ["0x01", "FLASHBOOT_B_IMAGE_OFFSET", "FLASHBOOT_B_IMAGE_LEN"], ["0x23", "APPLICATION_IMAGE_OFFSET", "APPLICATION_IMAGE_LEN"], ["0x25", "NV_IMAGE_OFFSET", "NV_IMAGE_LEN"], ["0x26", "FOTA_IMAGE_OFFSET", "FOTA_IMAGE_LEN"]]}