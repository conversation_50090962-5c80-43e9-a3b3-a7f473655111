set(LITEOS_SOURCE_DIR ${LITEOS_TOP_DIR})
message("${LITEOS_SOURCE_DIR}")
include(ExternalProject)

if (CMAKE_INSTALL_PREFIX STREQUAL /usr/local)
    set(CMAKE_INSTALL_PREFIX ${LITEOS_SOURCE_DIR}/output CACHE STRING "path for install()" FORCE)
    message(STATUS "No install prefix selected, default to ${CMAKE_INSTALL_PREFIX}.")
endif()

if(NOT DEFINED NAMESPACE_SUFFIX)
    set(NAMESPACE_SUFFIX "")
endif()
if(NOT DEFINED INSTALL_RELATIVE_PATH)
    set(INSTALL_RELATIVE_PATH .)
endif()

set(INSTALL_INCLUDE_DIR ${INSTALL_RELATIVE_PATH}/include)
set(INSTALL_LIBRARY_DIR ${INSTALL_RELATIVE_PATH}/lib)
set(INSTALL_ARCHIVE_DIR ${INSTALL_RELATIVE_PATH}/lib)
set(INSTALL_RUNTIME_DIR ${INSTALL_RELATIVE_PATH}/bin)
set(INSTALL_CONFIG_DIR  ${INSTALL_RELATIVE_PATH}/lib/cmake)
set(INSTALL_INCLUDE_LIST)

add_library(intf INTERFACE)

# install include start
FOREACH(LOS_API_INC ${LITEOS_EXT_API_INC} ${LITEOS_TARGETS_INCLUDE})
    if("${LOS_API_INC}" MATCHES "^${LITEOSTOPDIR}(.*)")
        STRING(REGEX REPLACE "${LITEOSTOPDIR}" "${INSTALL_INCLUDE_DIR}" INSTALL_API_PATH ${LOS_API_INC})
        list(APPEND INSTALL_INCLUDE_LIST "$<INSTALL_INTERFACE:${INSTALL_API_PATH}>")
        install(DIRECTORY ${LOS_API_INC}
            DESTINATION "${INSTALL_API_PATH}/../" FILES_MATCHING PATTERN "*.h"
        )
    endif()
ENDFOREACH()
set(INSTALL_TARGET_INCLUDE ${INSTALL_INCLUDE_DIR}/targets/include)
list(APPEND INSTALL_INCLUDE_LIST "$<INSTALL_INTERFACE:${INSTALL_TARGET_INCLUDE}>")
install(DIRECTORY ${LITEOSTOPDIR}/targets/${LITEOS_PLATFORM}/include
    DESTINATION "${INSTALL_TARGET_INCLUDE}/../" FILES_MATCHING PATTERN "*.h"
)
install(FILES ${LITEOS_PLATFORM_MENUCONFIG_H} DESTINATION ${INSTALL_TARGET_INCLUDE})
target_include_directories(intf INTERFACE ${INSTALL_INCLUDE_LIST})
# install include end 

# install options start
get_target_property(LITEOS_EXT_OPTIONS ${LOS_CC_PROP_INTF_PUB} INTERFACE_COMPILE_OPTIONS)
list(REMOVE_ITEM LITEOS_EXT_OPTIONS -include ${LITEOS_PLATFORM_MENUCONFIG_H})
target_compile_options(intf INTERFACE
    # auto added the header file LITEOS_PLATFORM_MENUCONFIG_H in all files
    -include menuconfig.h
    ${LITEOS_EXT_OPTIONS}
)
# install options end

# install definitions start
get_target_property(LITEOS_EXT_DEFINITIONS ${LOS_CC_PROP_INTF_PUB} INTERFACE_COMPILE_DEFINITIONS)
target_compile_definitions(intf INTERFACE ${LITEOS_EXT_DEFINITIONS})
# install definitions end

include(CMakePackageConfigHelpers)

# install libs
list(REMOVE_DUPLICATES LITEOS_DEP_LIBS_INT)
install(TARGETS ${LITEOS_DEP_LIBS_INT} intf
    EXPORT LiteOS${NAMESPACE_SUFFIX}-targets
    LIBRARY DESTINATION ${INSTALL_LIBRARY_DIR}
    ARCHIVE DESTINATION ${INSTALL_ARCHIVE_DIR}
    RUNTIME DESTINATION ${INSTALL_RUNTIME_DIR}
)

install(EXPORT LiteOS${NAMESPACE_SUFFIX}-targets
    NAMESPACE LiteOS${NAMESPACE_SUFFIX}::
    FILE LiteOS${NAMESPACE_SUFFIX}-targets.cmake
    DESTINATION ${INSTALL_CONFIG_DIR}
)

configure_package_config_file(${LITEOS_TOP_DIR}/build/cmake/LiteOSConfig.cmake.in
    ${CMAKE_CURRENT_BINARY_DIR}/LiteOS${NAMESPACE_SUFFIX}Config.cmake
    INSTALL_DESTINATION ${INSTALL_CONFIG_DIR}
    PATH_VARS INSTALL_RELATIVE_PATH INSTALL_INCLUDE_DIR INSTALL_LIBRARY_DIR INSTALL_ARCHIVE_DIR INSTALL_RUNTIME_DIR INSTALL_CONFIG_DIR NAMESPACE_SUFFIX
    INSTALL_PREFIX ${CMAKE_INSTALL_PREFIX}
)

install(FILES ${CMAKE_CURRENT_BINARY_DIR}/LiteOS${NAMESPACE_SUFFIX}Config.cmake
    DESTINATION ${INSTALL_CONFIG_DIR}
)

# install liteos.ld
if(EXISTS ${LITEOS_TOP_DIR}/targets/${LITEOS_PLATFORM}/liteos.ld)
    install(FILES ${LITEOS_TOP_DIR}/targets/${LITEOS_PLATFORM}/liteos.ld
        DESTINATION ${INSTALL_LIBRARY_DIR}
    )
else()
    install(FILES ${LITEOS_TOP_DIR}/build/liteos.ld
        DESTINATION ${INSTALL_LIBRARY_DIR}
    )
endif()

# install board.ld
if(EXISTS ${BOARD_LD_S_FILE})
    install(FILES ${BOARD_LD_FILE}
        DESTINATION ${INSTALL_LIBRARY_DIR}
    )
endif()
