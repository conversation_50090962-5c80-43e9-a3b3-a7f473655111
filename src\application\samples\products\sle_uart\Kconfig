#===============================================================================
# @brief    Kconfig file.
# Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
#===============================================================================

config SLE_UART_BUS
    int
    prompt "Set the UART BUS of the currrent sample."
    default 0
    depends on SAMPLE_SUPPORT_SLE_UART
    help
        This option means the UART BUS of the currrent sample.

choice
    prompt "Select sle uart type"
    default SAMPLE_SUPPORT_SLE_UART_SERVER
    config SAMPLE_SUPPORT_SLE_UART_SERVER
        bool "Enable SLE UART Server sample."
    config SAMPLE_SUPPORT_SLE_UART_CLIENT
        bool "Enable SLE UART Client sample."
endchoice

menu "Select SLE UART sample mode"
choice
    prompt "Select sle uart sample type"
    default SAMPLE_SUPPORT_NORMAL_TYPE
    config SAMPLE_SUPPORT_NORMAL_TYPE
        bool "Enable SLE UART normal sample."
    config SAMPLE_SUPPORT_LOW_LATENCY_TYPE
        bool "Enable SLE UART low latency sample."
endchoice

config SAMPLE_SUPPORT_PERFORMANCE_TYPE
    bool
    prompt "Set the currrent sample in PERFORMANCE mode."
    default n
    depends on SAMPLE_SUPPORT_LOW_LATENCY_TYPE
    help
        This option means the currrent sample is in performance mode.
endmenu