#!/usr/bin/env python3
# coding=utf-8

import csv
import struct
import hashlib
import os
from sys import version_info

file_dir = os.path.dirname(os.path.realpath(__file__))
g_root = os.path.realpath(os.path.join(file_dir, "..", "..", "..", "..", ".."))

def str_to_hex(s):
    return ' '.join([hex(ord(c)).replace('0x', '') for c in s])

def print_bytes(bytes):
    if version_info.major == 3:
        c = bytes.hex()
        print(c)
    else:
        c = bytes.encode('hex')
        print(c)

if __name__ == "__main__":
    number = 0
    value_len = 0
    buf = b''
    csv_dir = file_dir
    csv_path = os.path.join(csv_dir, 'efuse.csv')
    bin_path = os.path.join(g_root, 'interim_binary', 'bs20', 'bin', 'boot_bin',  'efuse_cfg.bin')

    # 用reader读取csv文件
    #Use the reader to read the CSV file.
    with open(csv_path, 'r') as csvFile:
        reader = csv.reader(csvFile)
        for line in reader:
            if(line[0] == "1"):
                size = int(line[3])
                if size % 32 == 0:
                    value_len = 4 * (size // 32)
                else:
                    value_len = 4 * (size // 32) + 4

                result = struct.pack('BBHHH', 0, 8, int(line[2]), size, value_len)
                value_str = line[4]
                value_list = value_str.split(" ")
                value_struct = b''
                for i in range(value_len // 4):
                    value = int(value_list[i], 16)
                    value_struct = value_struct + struct.pack('I', value)
                print_bytes(value_struct)
                buf = buf + result + value_struct
                number = number + 1
    header = struct.pack('BBHIII', 0, 48, number, len(buf) + 48, 0, 0)
    data = header + buf
    data_len = len(data)
    print("data size: ", data_len)
    if data_len % 64 != 0:
        max_size = int((data_len / 64)+1) * 64
        if data_len < max_size:
            data = data + bytes([0] * int(max_size - data_len))
    print("finally size: ", len(data))

    hash = hashlib.sha256(data).digest()
    bin_data = hash + data

    with open(bin_path, 'wb+') as f:
        f.write(bin_data)
