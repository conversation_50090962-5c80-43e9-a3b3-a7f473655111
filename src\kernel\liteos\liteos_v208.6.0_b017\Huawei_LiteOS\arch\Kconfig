# Copyright (c) Huawei Technologies Co., Ltd. 2019-2020. All rights reserved.

config LOSCFG_ARCH_FPU_ENABLE
    bool "Enable Floating Pointer Unit"
    default y
    depends on LOSCFG_ARCH_CORTEX_M4 || LOSCFG_ARCH_CORTEX_M7 || LOSCFG_ARCH_ARM_AARCH32 || LOSCFG_ARCH_ARM_AARCH64 || LOSCFG_ARCH_RISCV_ISA_F || LOSCFG_ARCH_LINGLONG
    help
      This option will bypass floating procedure in system.

config LOSCFG_ARCH_FPU_DOUBLE
    bool "Use 'D' Standard Extension"
    default n
    depends on LOSCFG_ARCH_RISCV_ISA_D && LOSCFG_ARCH_FPU_ENABLE && LOSCFG_ARCH_LINX_M
    help
      adds double-precision floating-point computational instructions compile.

choice
    prompt "FPU Mode"
    default LOSCFG_ARCH_FPU_SOFTFP
    # The FPU Mode Option Not Supported In Riscv
    depends on LOSCFG_ARCH_FPU_ENABLE && !LOSCFG_ARCH_RISCV_ISA_F
    help
      The FPU mode for compiler.

config LOSCFG_ARCH_FPU_SOFTFP
    bool "Use Mode softfp"
    # AArch64 has no specific FPU compile options like AArch32 does.
    depends on LOSCFG_ARCH_ARM_AARCH32
    help
      Choose "-mfloat-abi=softfp" for compiler.

config LOSCFG_ARCH_FPU_HARD
    bool "Use Mode hard"
    help
      Choose "-mfloat-abi=hard" for compiler.

endchoice

choice
    prompt "CPU Run Mode"
    depends on LOSCFG_ARCH_ARM_V8A || LOSCFG_ARCH_ARM_V8R
    default LOSCFG_ARCH_NON_SECURE_OS_MODE

config LOSCFG_ARCH_SECURE_MONITOR_MODE
    depends on LOSCFG_ARCH_ARM_V8A
    bool "Run On Secure Monitor Mode"
    help
      This option will make the system run on EL3.

config LOSCFG_ARCH_NON_SECURE_OS_MODE
    bool "Run On Non-Secure OS Mode"
    help
      This option will make the system run on EL1-NS.

config LOSCFG_ARCH_SECURE_OS_MODE
    bool "Run On Secure OS Mode"
    help
      This option will make the system run on EL1-S.
endchoice

config LOSCFG_APC_ENABLE
    bool "Enable Access Permission Control"
    default y
    help
      This option will Enable Access Permission Control

config LOSCFG_FPB_ENABLE
    bool "Enable Flash Patch"
    default n
    depends on LOSCFG_ARCH_ARM_CORTEX_M || LOSCFG_ARCH_LINXCORE_131
    help
      Answer Y to enable LiteOS to support Flash Patch

config RISCV_FPB_SET_PCO
    bool "Enable patch offset bigger than 1M"
    default y
    depends on LOSCFG_ARCH_RISCV32 && LOSCFG_FPB_ENABLE
    help
      Enable patch offset bigger than 1M by Set PCO(patch code branch offset) bit

config LOSCFG_LIB_CONFIGURABLE
    bool
    default n
    help
      Answer Y to enable LiteOS to support base kernel lib configurable

config LOSCFG_MULTI_BINARIES
    bool
    default n
    select LOSCFG_LIB_CONFIGURABLE
    help
      Answer Y to enable LiteOS to support multi binaries

config LOSCFG_ARCH_SOUP_ENABLE
    bool "Enable Hardware Stack Overflow/Underflow Protection (SOUP)"
    default y
    depends on LOSCFG_ARCH_LINGLONG && LOSCFG_ARCH_INTERRUPT_TAKEOVER
    help
      This option enables SOUP support.

config LOSCFG_ARCH_PMU
    bool
    default n
    help
      This option indicates the PMU implemented by hardware.

config LOSCFG_ARCH_VECTOR_ENABLE
    bool "Enable Vector Process Unit (VPU)"
    default n
    depends on LOSCFG_ARCH_RISCV_ISA_V && LOSCFG_ARCH_RISCV_TES && !LOSCFG_ARCH_INTERRUPT_PREEMPTION
    help
      This option enables VPU support.

osource "arch/common/Kconfig"
osource "arch/arm/Kconfig"
osource "arch/arm64/Kconfig"
osource "arch/xtensa/Kconfig"
osource "arch/riscv/Kconfig"
osource "arch/linglong/Kconfig"
