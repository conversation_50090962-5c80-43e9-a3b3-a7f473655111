cmake_minimum_required(VERSION 3.16.5)

project(LiteOS)
enable_language(C ASM)

# Disable in-source builds to prevent source tree pollution.
if("${CMAKE_SOURCE_DIR}" STREQUAL "${CMAKE_BINARY_DIR}")
    message(FATAL_ERROR "FATAL: In-source builds are not allowed.
        You should create a separate directory for build files.")
endif()

get_filename_component(LITEOS_TOP_DIR "${CMAKE_CURRENT_SOURCE_DIR}" ABSOLUTE)
set(LITEOSTOPDIR ${LITEOS_TOP_DIR})
set(GSYM_CFLAG "" CACHE INTERNAL "clear cache" FORCE)

message("CMAKE_CURRENT_SOURCE_DIR:${CMAKE_CURRENT_SOURCE_DIR}")
message("CMAKE_CURRENT_BINARY_DIR:${CMAKE_CURRENT_BINARY_DIR}")
message(STATUS "LITEOS_TOP_DIR: ${LITEOS_TOP_DIR}")

if(NOT DEFINED LOSCFG_CONFIG_PATH)
    set(LOSCFG_CONFIG_PATH ".config")
endif()
message("LOSCFG_CONFIG_PATH:${LOSCFG_CONFIG_PATH}")

############### getting board/platform's name ###############
include(${LITEOS_TOP_DIR}/build/cmake/function.cmake)
# filter-out and setup LOSCFG_PLATFORM only
IMPORT_BOARD_CONFIG(LOSCFG_PLATFORM= ${LOSCFG_CONFIG_PATH} IGNORE)
# remove "" maybe in LOSCFG_PLATFORM
string(REGEX REPLACE \" "" LITEOS_PLATFORM "${LOSCFG_PLATFORM}")

############### updating .config/menuconfig.h files ###############
set(LITEOS_MENUCONFIG_H ${LITEOSTOPDIR}/targets/menuconfig.h)
# LITEOS_CHECK_MENUCONFIG_H is generated by python script for compared with file generated by cmake.
set(LITEOS_CHECK_MENUCONFIG_H
    ${CMAKE_CURRENT_BINARY_DIR}/menuconfig/menuconfig.h)
set(LITEOS_PLATFORM_MENUCONFIG_H ${CMAKE_CURRENT_BINARY_DIR}/menuconfig/include/menuconfig.h
    CACHE STRING "LiteOS target menuconfig.h" FORCE)

if(EXISTS ${LITEOS_PLATFORM_MENUCONFIG_H})
    file(REMOVE ${LITEOS_PLATFORM_MENUCONFIG_H})
endif()
IMPORT_BOARD_CONFIG(LOSCFG_ ${LOSCFG_CONFIG_PATH} ${LITEOS_PLATFORM_MENUCONFIG_H})

# check kconfig is install or not
execute_process(
  COMMAND python3 ${LITEOS_TOP_DIR}/build/scripts/check_kconfiglib.py
  RESULT_VARIABLE PYTHON_RESULT
  OUTPUT_VARIABLE PYTHON_OUTPUT
  WORKING_DIRECTORY ${LITEOS_TOP_DIR}
)

set(BUILD_DIFF_MENUCONFIG_PROCESS)
# include kconfig.cmake and set BUILD_DIFF_MENUCONFIG_PROCESS only when kconfig is install
if(${PYTHON_OUTPUT} MATCHES "True")
    include(${LITEOS_TOP_DIR}/build/menuconfig/kconfig.cmake)
    set(BUILD_DIFF_MENUCONFIG_PROCESS
        COMMAND bash -c "sh ${LITEOSTOPDIR}/build/scripts/diff_files.sh ${LITEOS_CHECK_MENUCONFIG_H} ${LITEOS_PLATFORM_MENUCONFIG_H}"
    )
endif()

############### setup cmake env ###############
include(${LITEOS_TOP_DIR}/build/cmake/get_env.cmake)

if(NOT ${LOSCFG_FAMILY} STREQUAL "")
    set(LITEOS_PLATFORM ${LOSCFG_FAMILY}/${LITEOS_PLATFORM})
endif()

if(NOT ${LOSCFG_PLATFORM_EXTENDED_FEATURE} STREQUAL "")
    set(LITEOS_PLATFORM ${LITEOS_PLATFORM}/${LOSCFG_PLATFORM_EXTENDED_FEATURE})
endif()

set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
if(${CMAKE_GENERATOR} MATCHES "Unix Makefiles")
    # remove abspath influence on generated bin files
    if(NOT LOSCFG_COMPILER_XTENSA_32)
        # ninja don't support, Makefiles support it.
        set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wno-builtin-macro-redefined -D__FILE__='\"$(notdir $(abspath $<))\"'")
        set(CMAKE_ASM_FLAGS "${CMAKE_ASM_FLAGS} -Wno-builtin-macro-redefined -D__FILE__='\"$(notdir $(abspath $<))\"'")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-builtin-macro-redefined -D__FILE__='\"$(notdir $(abspath $<))\"'")
    endif()
    # file name extension changed to *.o instead of *.c.o and *.S.o
    set(CMAKE_C_OUTPUT_EXTENSION_REPLACE 1)
    set(CMAKE_ASM_OUTPUT_EXTENSION_REPLACE 1)
    # customize cmake ar commands to prevent timestamp influence on *.a
    set(CMAKE_C_ARCHIVE_CREATE "<CMAKE_AR> crD <TARGET> <LINK_FLAGS> <OBJECTS>")
    set(CMAKE_C_ARCHIVE_APPEND "<CMAKE_AR> rD <TARGET> <LINK_FLAGS> <OBJECTS>")
    set(CMAKE_C_ARCHIVE_FINISH "<CMAKE_RANLIB> -D <TARGET>")
endif()

############### generating lib, *.bin and *.map files ###############
include(${LITEOS_TOP_DIR}/build/cmake/config.cmake)
add_dependencies(${LOS_CC_PROP_INTF_PUB} savemenuconfig)

set(BUILD_LINKER_PROCESS)
set(BUILD_LINKER_PRE_PROCESS)
set(BUILD_LINKER_POST_PROCESS)
set(LITEOS_TARGET vs_server)
set(LITEOS_BASELIB_LD ${LITEOS_BASELIB})
list(TRANSFORM LITEOS_BASELIB_LD PREPEND -l)
set(LITEOS_LD_COMMAND ${CMAKE_LINKER} ${LITEOS_LDFLAGS} ${LITEOS_TABLES_LDFLAGS} ${LITEOS_DYNLDFLAGS})

##### make wow && make scatter && make wow_scatter #####
include (${MK_PATH}/wow_scatter.cmake)

include (${MK_PATH}/aslr.cmake)
include (${MK_PATH}/dynload.cmake)

# touch empty_file.c file
if (NOT EXISTS ${OUT}/empty_file.c)
    file(WRITE ${OUT}/empty_file.c "/* do nothing */\n")
endif()

if (NOT LOSCFG_MULTI_BINARIES)
    set(BUILD_LINKER_PROCESS
        COMMAND ${LITEOS_LD_COMMAND} -Map=${OUT}/${LITEOS_TARGET}.map -o ${OUT}/${LITEOS_TARGET} --start-group ${LITEOS_BASELIB_LD} --end-group
        COMMAND ${CMAKE_OBJCOPY} -O binary ${OUT}/${LITEOS_TARGET} ${OUT}/${LITEOS_TARGET}.bin
        COMMAND ${CMAKE_OBJCOPY} --only-keep-debug ${OUT}/${LITEOS_TARGET} ${OUT}/${LITEOS_TARGET}.dbg
    )
    if (NOT DEFINED READELF_IS_EXISTED OR READELF_IS_EXISTED)
        list(APPEND BUILD_LINKER_PROCESS
            COMMAND ${CMAKE_READELF} -a ${OUT}/${LITEOS_TARGET} > ${OUT}/${LITEOS_TARGET}.readelf
        )
    endif()
    if (NOT DEFINED OBJDUMP_IS_EXISTED OR OBJDUMP_IS_EXISTED)
        list(APPEND BUILD_LINKER_PROCESS
            COMMAND ${CMAKE_OBJDUMP} -t ${OUT}/${LITEOS_TARGET} |sort > ${OUT}/${LITEOS_TARGET}.sym.sorted
            COMMAND ${CMAKE_OBJDUMP} -d -h ${OUT}/${LITEOS_TARGET} > ${OUT}/${LITEOS_TARGET}.asm
        )
    endif()
    if (NOT DEFINED SIZE_IS_EXISTED OR SIZE_IS_EXISTED)
        list(APPEND BUILD_LINKER_PROCESS
            COMMAND ${PYTHON3} ${SCRIPTS_PATH}/analysis_liba_size.py ${SIZE} ${OUT}/ ${OUT}/lib/
        )
    endif()
else()
    include(${MK_PATH}/multi_binaries.cmake)
endif()

add_custom_target(${LITEOS_TARGET}
    DEPENDS ${LITEOS_DEP_LIBS_INT}
    DEPENDS ${LITEOS_DEP_OBJS_INT}
    ${BUILD_LINKER_PRE_PROCESS}
    ${BUILD_LINKER_PROCESS}
    ${BUILD_LINKER_POST_PROCESS}
    ${BUILD_DIFF_MENUCONFIG_PROCESS}
    COMMAND bash -c "sh ${LITEOSTOPDIR}/build/cmake/build_install.sh ${LITEOSTOPDIR} ${CMAKE_INSTALL_PREFIX} ${CMAKE_CURRENT_BINARY_DIR}"
    BYPRODUCTS ${OUT}/${LITEOS_TARGET}.bin ${OUT}/${LITEOS_TARGET}.dbg ${OUT}/${LITEOS_TARGET}.sym.sorted
    BYPRODUCTS ${OUT}/${LITEOS_TARGET} ${OUT}/${LITEOS_TARGET}.asm ${OUT}/${LITEOS_TARGET}.map
    BYPRODUCTS ${OUT}/empty_file.c ${LITEOS_PLATFORM_MENUCONFIG_H}
)

##### cc los_dynload_gsymbol.c file #####
set(GSYM_FILE ${OUT}/dynload_dir/dynload_sym/los_dynload_gsymbol.c)
if(EXISTS ${GSYM_FILE})
    set(DYNLOAD_SYM_INCLUDE_PATH ${LITEOSTOPDIR}/kernel/extended/dynload/include)
    set(DYNLOAD_SYM_OUTPUT_FILE ${OUT}/dynload_dir/dynload_sym/los_dynload_gsymbol.o)
    add_custom_target(dynload_sym
        COMMAND ${CC} ${GSYM_CFLAG} -c ${GSYM_FILE} -I ${DYNLOAD_SYM_INCLUDE_PATH} -I ${LITEOS_KERNEL_INCLUDE} -o ${DYNLOAD_SYM_OUTPUT_FILE}
    )
    add_dependencies(${LITEOS_TARGET} dynload_sym)
endif()

if(DEFINED INSTALL_LIB_CMAKE)
    include(${LITEOS_TOP_DIR}/build/cmake/liteos_install_package.cmake)
    unset(INSTALL_LIB_CMAKE)
    unset(INSTALL_LIB_CMAKE CACHE) # <---- this is the important
endif()

if(EXISTS ${ASLR_REL_FILE})
    set(ASLR_REL_INCLUDE_PATH -I ${LITEOSTOPDIR}/kernel/extended/dynload/include
        -I ${LITEOSTOPDIR}/kernel/include)
    add_custom_target(aslr_rel
        COMMAND ${CC}  -c  ${ASLR_REL_FILE} ${ASLR_REL_INCLUDE_PATH} ${GSYM_CFLAG} -o ${ASLR_REL_OBJECT}
        COMMAND bash -c "mv ${ASLR_REL_FILE} ${ASLR_REL_FILE}.bak"
        BYPRODUCTS ${ASLR_REL_OBJECT}
    )
    add_dependencies(${LITEOS_TARGET} aslr_rel)
endif()

############### output ld info ###############
message(" LITEOS_LDFLAGS:${LITEOS_LDFLAGS}")
message(" LITEOS_TABLES_LDFLAGS:${LITEOS_TABLES_LDFLAGS}")
message(" LITEOS_DYNLDFLAGS:${LITEOS_DYNLDFLAGS}")
message("CMAKE_INSTALL_PREFIX:${CMAKE_INSTALL_PREFIX}")
