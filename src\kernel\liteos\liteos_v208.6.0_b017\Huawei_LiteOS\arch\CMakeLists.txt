set(MODULE_y common)
list(APPEND MODULE_${LOSCFG_ARCH_ARM_AARCH32} arm)
list(APPEND MODULE_${LOSCFG_ARCH_ARM_AARCH64} arm64)
list(APPEND MODULE_${LOSCFG_ARCH_XTENSA} xtensa)
list(APPEND MODULE_${LOSCFG_ARCH_RISCV32} riscv)
list(APPEND MODULE_${LOSCFG_ARCH_LINGLONG} linglong)

FOREACH(CUR_MODULE_y ${MODULE_y})
    add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/${CUR_MODULE_y})
END<PERSON><PERSON>EACH(CUR_MODULE_y)
