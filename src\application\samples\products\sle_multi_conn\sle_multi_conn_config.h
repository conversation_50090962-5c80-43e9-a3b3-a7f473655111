/**
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2025. All rights reserved.
 *
 * Description: SLE MULTI_CONN configuration for 16 connections. \n
 *
 * History: \n
 * 2024-01-01, Create file for 16 connections configuration. \n
 */

#ifndef SLE_MULTI_CONN_CONFIG_H
#define SLE_MULTI_CONN_CONFIG_H

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cplusplus */
#endif /* __cplusplus */

/* 16连接优化配置 */
#define SLE_MULTI_CONN_16_OPTIMIZED 1

/* 连接参数优化 */
#define SLE_CONN_INTERVAL_MIN_16DEV 0x30    // 6ms，适合16设备
#define SLE_CONN_INTERVAL_MAX_16DEV 0x30    // 6ms
#define SLE_CONN_LATENCY_16DEV 0            // 低延迟
#define SLE_CONN_TIMEOUT_16DEV 0x258        // 6s超时

/* 扫描参数优化 */
#define SLE_SCAN_INTERVAL_16DEV 0x60        // 60ms扫描间隔
#define SLE_SCAN_WINDOW_16DEV 0x30          // 30ms扫描窗口

/* 内存优化配置 */
#define SLE_CONN_POOL_SIZE_16DEV (16 * 256) // 每连接256字节
#define SLE_DATA_BUFFER_SIZE_16DEV 128      // 数据缓冲区大小

/* 调度优化配置 */
#define SLE_CONN_BATCH_SIZE 4               // 批量连接大小
#define SLE_CONN_RETRY_COUNT 3              // 连接重试次数
#define SLE_CONN_RETRY_INTERVAL_MS 500      // 重试间隔

/* 性能监控配置 */
#define SLE_PERF_MONITOR_ENABLED 1          // 启用性能监控
#define SLE_PERF_LOG_INTERVAL_MS 5000       // 性能日志间隔

/* 功耗优化配置 */
#define SLE_POWER_SAVE_MODE 1               // 启用功耗优化
#define SLE_IDLE_TIMEOUT_MS 10000           // 空闲超时

#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cplusplus */
#endif /* __cplusplus */

#endif /* SLE_MULTI_CONN_CONFIG_H */
