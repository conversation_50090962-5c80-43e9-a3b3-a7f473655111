/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description:  Interrupt DRIVER
 *
 * Create: 2021-06-16
 */
#include "stdint.h"
#include "chip_core_irq.h"

uint8_t g_aucIntPri[BUTT_IRQN] = {
    0,   // USER_SOFTWARE_INT_IRQn            = 0,
    0,   // SUPERVISOR_SOFTWARE_INT_IRQn      = 1,
    0,   // RESERVED_INT2_IRQn                = 2,
    1,   // MACHINE_SOFTWARE_INT_IRQn         = 3,
    0,   // USER_TIMER_INT_IRQn               = 4,
    0,   // SUPERVISOR_TIMER_INT_IRQn         = 5,
    0,   // RESERVED_INT6_IRQn                = 6,
    0,   // MACHINE_TIMER_INT_IRQn            = 7,
    0,   // USER_EXTERNAL_INT_IRQn            = 8,
    0,   // SUPERVISOR_EXTERNAL_INT_IRQn      = 9,
    0,   // RESERVED_INT10_IRQn               = 10,
    1,   // MACHINE_EXTERNAL_INT_IRQn         = 11,
    7,   // NON_MASKABLE_INT_IRQn             = 12,
    0,   // RESERVED_INT13_IRQn               = 13,
    0,   // RESERVED_INT14_IRQn               = 14,
    0,   // RESERVED_INT15_IRQn               = 15,
    0,   // RESERVED_INT16_IRQn               = 16,
    0,   // RESERVED_INT17_IRQn               = 17,
    0,   // RESERVED_INT18_IRQn               = 18,
    0,   // RESERVED_INT19_IRQn               = 19,
    0,   // RESERVED_INT20_IRQn               = 20,
    0,   // RESERVED_INT21_IRQn               = 21,
    0,   // RESERVED_INT22_IRQn               = 22,
    0,   // RESERVED_INT23_IRQn               = 23,
    0,   // RESERVED_INT24_IRQn               = 23,
    0,   // RESERVED_INT25_IRQn               = 25,

    1,   // RESERVED_INT0_IRQn                =  0,
    1,   // RESERVED_INT1_IRQn                =  1,
    1,   // MCU_INT0_IRQn                     =  2,
    1,   // MCU_INT1_IRQn                     =  3,
    1,   // RESERVED_INT4_IRQn                =  4,
    1,   // RESERVED_INT5_IRQn                =  5,
    1,   // RESERVED_INT6_IRQn                =  6,
    1,   // RESERVED_INT7_IRQn                =  7,
    1,   // GPIO_0_IRQn                       =  8,
    1,   // GPIO_1_IRQn                       =  9,
    1,   // RESERVED_INT10_IRQn               = 10,
    1,   // RESERVED_INT11_IRQn               = 11,
    1,   // RESERVED_INT12_IRQn               = 12,
    1,   // UART_L0_IRQn                      = 13,
    1,   // RESERVED_INT14_IRQn               = 14,
    1,   // UART_H0_IRQn                      = 15,
    1,   // UART_H1_IRQn                      = 16,
    1,   // QSPI0_2CS_IRQn                    = 17,
    1,   // QSPI1_2CS_IRQn                    = 18,
    1,   // SPI4_S_IRQn                       = 19,
    1,   // KEY_SCAN_IRQn                     = 20,
    1,   // M_WAKEUP_IRQn                     = 21,
    1,   // M_SLEEP_IRQn                      = 22,
    1,   // RTC_0_IRQn                        = 23,
    1,   // RTC_1_IRQn                        = 24,
    1,   // RTC_2_IRQn                        = 25,
    1,   // RTC_3_IRQn                        = 26,
    1,   // TIMER_0_IRQn                      = 27,
    1,   // TIMER_1_IRQn                      = 28,
    1,   // TIMER_2_IRQn                      = 29,
    1,   // TIMER_3_IRQn                      = 30,
    1,   // M_SDMA_IRQn                       = 31,
    1,   // M_DMA_IRQn                        = 32,
    1,   // SPI_M_S_0_IRQn                    = 33,
    1,   // SPI_M_S_1_IRQn                    = 34,
    1,   // SPI_M_IRQn                        = 35,
    1,   // I2C_0_IRQn                        = 36,
    1,   // I2C_1_IRQn                        = 37,
    1,   // I2C_2_IRQn                        = 38,
    1,   // SPI3_MS_IRQn                      = 39,
    1,   // EFLASH_INT_IRQn                   = 40,
    1,   // RESERVED_INT41_IRQn               = 41,
    1,   // RESERVED_INT42_IRQn               = 42,
    1,   // RESERVED_INT43_IRQn               = 43,
    1,   // SEC_INT_IRQn                      = 44,
    1,   // PWM_0_IRQn                        = 45,
    1,   // PWM_1_IRQn                        = 46,
    1,   // PWM_2_IRQn                        = 47,
    1,   // PWM_3_IRQn                        = 48,
    1,   // PWM_4_IRQn                        = 49,
    1,   // PWM_5_IRQn                        = 50,
    1,   // RESERVED_INT51_IRQn               = 51,
    0,   // PMU_CMU_ERR_IRQn                  = 52,
    1,   // RESERVED_INT53_IRQn               = 53,
    1,   // RESERVED_INT54_IRQn               = 54,
    1,   // MEM_SUB_MONITOR_INT_IRQn          = 55,
    1,   // B_SUB_MONITOR_INT_IRQn            = 56,
    1,   // COMRAM_MONITOR_IRQn               = 57,
    1,   // EH2H_BRG_IRQn                     = 58,
    1,   // PMU2_CLK_32K_CALI_IRQn            = 59,
    1,   // B_WDT_IRQn                        = 60,
    1,   // TSENSOR_IRQn                      = 61,
    1,   // QDEC_IRQn                         = 62,
    1,   // USB_IRQn                          = 63,
};
