#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
# Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
"""Description: Huawei LiteOS Kconfig Tool"""

from __future__ import absolute_import
import os
import sys
from kconfiglib import Kconfig
from menuconfig import menuconfig


def mconf_set_env(style, conf, header):
    """
    Set Kconfig Env
    """
    os.environ["MENUCONFIG_STYLE"] = style
    os.environ["KCONFIG_CONFIG"] = conf
    os.environ["KCONFIG_CONFIG_HEADER"] = header
    os.environ["KCONFIG_AUTOHEADER"] = os.path.join(os.environ.get('KCONFIG_OUTPUT_MENUCONFIG_PATH'))
    os.environ["CONFIG_"] = ""


def mconfig(argv):
    '''
    Build Menuconfig
    '''
    kconfig = os.path.join("build", "menuconfig", "config.in")
    display_style = "default selection=fg:white,bg:blue"
    target_conf = os.path.join(".config")
    header = "# Generated by Huawei LiteOS Kconfig Tool"
    mconf_set_env(display_style, target_conf, header)
    kconf = Kconfig(filename=kconfig)
    if len(argv) == 2 and argv[1] == 'savemenuconfig':
        kconf.load_config()
        print(kconf.write_config()) # savemenuconfig
    elif len(argv) == 2 and argv[1] == 'defconfig':
        kconf.load_allconfig("alldef.config")
        print(kconf.write_config()) # defconfig
    elif len(argv) == 2 and argv[1] == 'allyesconfig':
        kconf.warn = False
        for sym in kconf.unique_defined_syms:
            sym.set_value(1 if sym.choice else 2)
        for choice in kconf.unique_choices:
            choice.set_value(2)
        kconf.warn = True
        kconf.load_allconfig("allyes.config")
        print(kconf.write_config()) # allyesconfig
    elif len(argv) == 2 and argv[1] == 'allnoconfig':
        kconf.warn = False
        for sym in kconf.unique_defined_syms:
            sym.set_value(2 if sym.is_allnoconfig_y else 0)
        kconf.warn = True
        kconf.load_allconfig("allno.config")
        print(kconf.write_config()) # allnoconfig
    else:
        menuconfig(kconf)   # menuconfig
    kconf.write_autoconf()

if __name__ == "__main__":
    mconfig(sys.argv)
