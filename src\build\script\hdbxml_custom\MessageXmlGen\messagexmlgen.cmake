function(messagexml TARGETDIR)
    string(REPLACE "${CMAKE_SOURCE_DIR}/" "" RE_TARGETDIR ${TARGETDIR})
    set(TARGET "${RE_TARGETDIR}/${CORE}_messages")
    set(STACK_XML "${RE_TARGETDIR}/stack.xml")
    set(MESSAGES_XML "${TARGET}.xml")
    set(MESSAGES_EXTERNAL_XML "${TARGET}_external.xml")
    set(MESSAGES_HFILE "${TARGET}.h")
    set(MESSAGES_CPPFILE "${TARGET}.i")
    set(MESSAGEXMLCONFIG ${CMAKE_SOURCE_DIR}/build/script/hdbxml_custom/MessageXmlGen/MessageXmlGen_${CORE}.cfg)
    set(MESSAGERULES ${CMAKE_SOURCE_DIR}/build/script/hdbxml_custom/MessageXmlGen/MessageRules_${CORE}.xml)
    if(EXISTS "${CMAKE_SOURCE_DIR}/${MESSAGES_HFILE}")
        message(STATUS "REMOVE ${CMAKE_SOURCE_DIR}/${MESSAGES_HFILE}")
        file(REMOVE "${CMAKE_SOURCE_DIR}/${MESSAGES_HFILE}")
    endif()
    list(LENGTH WANTED_HEADERS LI_LEN)
    set(INDEX 1)
    foreach(h_file ${WANTED_HEADERS})
        string(REPLACE "${CMAKE_SOURCE_DIR}/" "" re_h_file ${h_file})
        if (${INDEX} EQUAL ${LI_LEN})
            file(APPEND ${MESSAGES_HFILE} "#include \"${re_h_file}\"")
        else()
            file(APPEND ${MESSAGES_HFILE} "#include \"${re_h_file}\"\n")
            math(EXPR INDEX "${INDEX}+1")
        endif()
    endforeach()

    set(XML_CPPINCFLAGS)
    foreach(inc ${INCLUDES})
        string(REPLACE "${CMAKE_SOURCE_DIR}/" "" re_inc ${inc})
        list(APPEND XML_CPPINCFLAGS "-I${re_inc} ")
    endforeach()


    set(XML_CPPDEFFLAGS)
# TODO 解析json时会给public_define添加-D前缀
    foreach(def ${XML_DEFINES})
        string(FIND "${def}" "-D" RET)
        if(${RET} EQUAL 0)
            list(APPEND XML_CPPDEFFLAGS "${def} ")
        else()
            list(APPEND XML_CPPDEFFLAGS "-D${def} ")
        endif()
    endforeach()

    file(WRITE ${RE_TARGETDIR}/database_include.rsp ${XML_CPPINCFLAGS})
    file(WRITE ${RE_TARGETDIR}/database_define.rsp ${XML_CPPDEFFLAGS})

    execute_process(COMMAND ${CMAKE_C_COMPILER} -E @${RE_TARGETDIR}/database_include.rsp @${RE_TARGETDIR}/database_define.rsp ${MESSAGES_HFILE}
                    WORKING_DIRECTORY  ${CMAKE_SOURCE_DIR}
                    RESULT_VARIABLE RES
                    ERROR_VARIABLE ERR
                    OUTPUT_VARIABLE OUT)
    if(NOT (${RES} EQUAL 0))
        message(FATAL_ERROR "${ERR}")
    endif()
    set(MESSAGES_IFILE "${TARGETDIR}/${CORE}_messages.i")
    if(EXISTS ${MESSAGES_IFILE})
        file(REMOVE ${MESSAGES_IFILE})
    endif()
    file(APPEND ${MESSAGES_CPPFILE} "${OUT}")
    message(STATUS ${Python3_EXECUTABLE})
    message(STATUS "${Python3_EXECUTABLE} ${CMAKE_SOURCE_DIR}/build/script/hdbxml_custom/MessageXmlGen/MessageXmlGen.py ${MESSAGES_CPPFILE} ${MESSAGES_XML} ${MESSAGEXMLCONFIG} ${CORE} ${CORE} ${MESSAGERULES}")
    execute_process(COMMAND ${Python3_EXECUTABLE} ${CMAKE_SOURCE_DIR}/build/script/hdbxml_custom/MessageXmlGen/MessageXmlGen.py ${MESSAGES_CPPFILE} ${MESSAGES_XML} ${MESSAGEXMLCONFIG} ${CORE} ${CORE} ${MESSAGERULES}
                    WORKING_DIRECTORY  ${CMAKE_SOURCE_DIR}
                    RESULT_VARIABLE RES
                    ERROR_VARIABLE ERR
                    OUTPUT_VARIABLE OUT)
    if(NOT (${RES} EQUAL 0))
        message(FATAL_ERROR "${ERR}")
    else()
    message(STATUS "${OUT}")
    endif()
    # create_database()
endfunction()

function(messagexml_generate_defs)
    set(SOURCE)
    set(SOURCEPATHS)
    foreach(h ${ALL_HEADERS})
        get_filename_component(SOURCEPATH ${h} ABSOLUTE)
        list(FIND SOURCEPATHS ${SOURCEPATH} RET)
        if(${RET} EQUAL -1)
            list(APPEND SOURCE ${h})
            list(APPEND SOURCEPATHS ${SOURCEPATH})
        endif()
    endforeach()
    set(WANTED_HEADERS)
    foreach(h ${SOURCE})
        get_filename_component(H_FILENAME ${h} NAME)
        if((${H_FILENAME} STREQUAL "rf_log_messages.h") AND (${CORE} STREQUAL "acore"))
            continue()
        endif()
        if((${H_FILENAME} MATCHES ".*_messages.h$") OR (${H_FILENAME} MATCHES ".*_if.h$") OR
        (${H_FILENAME} MATCHES ".*_messages_internal.h$") OR (${H_FILENAME} MATCHES ".*_message.h$"))
            list(APPEND WANTED_HEADERS ${h})
        endif()
    endforeach()

    get_property(MSGDEF_HEADERS GLOBAL PROPERTY TARGET_MSGDEF_HEADERS)
    foreach(fsm ${MSGDEF_HEADERS})
        list(APPEND WANTED_HEADERS ${fsm})
    endforeach()
    # message(STATUS ${WANTED_HEADERS})
    list(INSERT INCLUDES 0 ${MSGDEF_INCLUDES})
    list(INSERT INCLUDES 0 ${MSGDEF_INCLUDES_DFX})
    list(APPEND INCLUDES ${CMAKE_SOURCE_DIR})
    list(APPEND INCLUDES ${CMAKE_SOURCE_DIR}/kernel/liteos/liteos_v208.6.0_b017_cat1/Huawei_LiteOS/platform/libsec/include)
    set(XML_DEFINES ${ALL_DEFINES})
    list(APPEND XML_DEFINES "SOC_DO_NOT_PACK_STRUCTS")
    messagexml("${PROJECT_BINARY_DIR}")
endfunction()

function(build_xml)
    set(MSGDEF_INCLUDES "protocol/${CHIP}/include/message_headers/msgdef")
    set(MSGDEF_INCLUDES_DFX "protocol/${CHIP}/include/message_headers/include")
    set(ALL_HEADERS)
    set(INCLUDES ${ALL_HEADER_DIRS})
    foreach(p ${INCLUDES})
        file(GLOB HEADERS "${p}/*.h")
        foreach(h ${HEADERS})
            list(APPEND ALL_HEADERS ${h})
        endforeach()
    endforeach()
    messagexml_generate_defs()
endfunction()