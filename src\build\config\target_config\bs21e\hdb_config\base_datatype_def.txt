uint32_t      32      0
uint16_t      16      0
uint8_t       8       0

int32_t       32      1
int16_t       16      1
int8_t        8       1

enum        32      0
HSO_ENUM    32      0

u32         32      0
u16         16      0
u8          8       0

s32         32      1
s16         16      1
s8          8       1
BOOL        8       0

TD_U8A      8       0
TD_U16A     16      0
TD_CHARTA   8       1  1

TD_U32      32      0
TD_U16      16      0
TD_U8       8       0
TD_S32      32      1
TD_S16      16      1
TD_S8       8       1
TD_BOOL     8       0
TD_CHAR     8       1  1
char        8       1  1

long        32      1
TD_PVOID    32      0
TD_PBYTE    32      0

td_u32      32      0
td_u16      16      0
td_u8       8       0
td_s32      32      1
td_s16      16      1
td_s8       8       1
td_bool     8       0
td_char     8       1  1

td_pvoid    32      0
td_pbyte    32      0
uintptr_t   32      0

td_u64      64      0
td_uintptr_t   32      0
td_void    32      0