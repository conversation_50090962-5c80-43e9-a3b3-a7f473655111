
Miss_funcptr: <PERSON><PERSON>_MemAlloc ... 
Remarks_here: 0x465ca LOS_MemAlloc 

Miss_funcptr: panic ... 
Remarks_here: 0x9012babc panic 

Miss_funcptr: log_trigger ... 
Remarks_here: 0x9012c2e6 log_trigger 

Miss_funcptr: LOS_HwiClear ... 
Remarks_here: 0x45812 LOS_HwiClear 

Miss_funcptr: OsTaskSwitchCheck ... 
Remarks_here: 0x461d0 OsTaskSwitchCheck 

Miss_funcptr: LOS_TaskCreateOnly ... 
Remarks_here: 0x9011857a LOS_TaskCreateOnly 

Miss_funcptr: hci_gle_send_evt_msg ... 
Remarks_here: 0x90131c92 hci_gle_send_evt_msg 

Miss_funcptr: hci_c2h_msg_print ... 
Remarks_here: 0x90131ba2 hci_c2h_msg_print 

Miss_funcptr: log_oam_status_store_trigger_callback ... 
Remarks_here: 0x9012c17c log_oam_status_store_trigger_callback 

Miss_funcptr: LOS_HwiDisable ... 
Remarks_here: 0x90117b74 LOS_HwiDisable 

Miss_funcptr: LOS_HwiDelete ... 
Remarks_here: 0x90117b30 LOS_HwiDelete 

Miss_funcptr: LOS_HwiCreate ... 
Remarks_here: 0x90117a72 LOS_HwiCreate 

Miss_funcptr: LOS_HwiCreate ... 
Remarks_here: 0x90117a72 LOS_HwiCreate 

Miss_funcptr: LOS_HwiSetPriority ... 
Remarks_here: 0x90117b8c LOS_HwiSetPriority 

Miss_funcptr: LOS_HwiEnable ... 
Remarks_here: 0x90117b5c LOS_HwiEnable 

Miss_funcptr: drv_cipher_trng_get_random ... 
Remarks_here: 0x90124b10 drv_cipher_trng_get_random 

Miss_funcptr: drv_cipher_trng_get_random ... 
Remarks_here: 0x90124b10 drv_cipher_trng_get_random 

Miss_funcptr: drv_cipher_trng_get_random ... 
Remarks_here: 0x90124b10 drv_cipher_trng_get_random 

Miss_funcptr: drv_cipher_trng_get_random ... 
Remarks_here: 0x90124b10 drv_cipher_trng_get_random 

Miss_funcptr: evt_prog_program_eeq ... 
Remarks_here: 0x49aaa evt_prog_program_eeq 

Miss_funcptr: es_prog_multi_eeq ... 
Remarks_here: 0x49a3e es_prog_multi_eeq 

Miss_funcptr: uapi_clock_control ... 
Remarks_here: 0x90126188 uapi_clock_control 

Miss_funcptr: uapi_pmu_ldo_set_voltage ... 
Remarks_here: 0x90126352 uapi_pmu_ldo_set_voltage 

Miss_funcptr: hci_gle_send_evt_msg_without_log ... 
Remarks_here: 0x90131c5e hci_gle_send_evt_msg_without_log 

Miss_funcptr: fsm_event_func_external ... 
Remarks_here: 0x90156ff2 fsm_event_func_external 

Miss_funcptr: fsm_inst_free ... 
Remarks_here: 0x90156cf0 fsm_inst_free 

Miss_funcptr: ble_gap_internal_cbk ... 
Remarks_here: 0x9015fdd0 ble_gap_internal_cbk 

Miss_funcptr: api_h2c_write ... 
Remarks_here: 0x90132040 api_h2c_write 

Miss_funcptr: hci_bt_send_evt_msg ... 
Remarks_here: 0x90131c6c hci_bt_send_evt_msg 

Miss_funcptr: hci_bt_cmd_get_dest_id ... 
Remarks_here: 0x90132138 hci_bt_cmd_get_dest_id 

Miss_funcptr: hci_bt_cmd_get_dest_id ... 
Remarks_here: 0x90132138 hci_bt_cmd_get_dest_id 

Miss_funcptr: hci_gle_cmd_get_dest_id ... 
Remarks_here: 0x9013249a hci_gle_cmd_get_dest_id 

Miss_funcptr: hci_cbk_traverse ... 
Remarks_here: 0x901626b6 hci_cbk_traverse 

Miss_funcptr: hci_cbk_traverse ... 
Remarks_here: 0x901626b6 hci_cbk_traverse 

Miss_funcptr: hci_ble_rx_acl_data ... 
Remarks_here: 0x90131cbc hci_ble_rx_acl_data 

Miss_funcptr: hci_bt_send_evt_msg_without_log ... 
Remarks_here: 0x90131c52 hci_bt_send_evt_msg_without_log 

Miss_funcptr: hci_local_remove_one ... 
Remarks_here: 0x90162834 hci_local_remove_one 

Miss_funcptr: hci_command_remove ... 
Remarks_here: 0x901615fa hci_command_remove 

Miss_funcptr: hci_controller_config_create_le_nodes_two ... 
Remarks_here: 0x90164c62 hci_controller_config_create_le_nodes_two 

Miss_funcptr: hci_tl_reg ... 
Remarks_here: 0x90164a2e hci_tl_reg 

Miss_funcptr: le_local_set_own_addr ... 
Remarks_here: 0x90163df4 le_local_set_own_addr 

Miss_funcptr: memmove ... 
Remarks_here: 0x9011716a memmove 

Miss_funcptr: fsm_q_buf_cleanup ... 
Remarks_here: 0x901569b2 fsm_q_buf_cleanup 

Miss_funcptr: __uflow ... 
Remarks_here: 0x90117326 __uflow 

Miss_funcptr: __toread ... 
Remarks_here: 0x90117388 __toread 

Miss_funcptr: OsHwiDel.isra.3 ... 
Remarks_here: 0x90117a1a OsHwiDel.isra.3 

Miss_funcptr: OsExcHandleEntry ... 
Remarks_here: 0x90119186 OsExcHandleEntry 

Miss_funcptr: nmiHandle ... 
Remarks_here: 0x901193e0 nmiHandle 

Miss_funcptr: trapEntry ... 
Remarks_here: 0x901194b0 trapEntry 

Miss_funcptr: evt_task_gle_acb_instant_prog ... 
Remarks_here: 0x90194e38 evt_task_gle_acb_instant_prog 

Miss_funcptr: evt_task_gle_acb_change_seg_num ... 
Remarks_here: 0x9019557a evt_task_gle_acb_change_seg_num 

Miss_funcptr: evt_task_gle_acb_low_latency_es_context_config ... 
Remarks_here: 0x9019571a evt_task_gle_acb_low_latency_es_context_config 

Miss_funcptr: evt_task_gle_acb_init_link_segment ... 
Remarks_here: 0x90119734 evt_task_gle_acb_init_link_segment 

Miss_funcptr: hal_sfc_reg_flash_opreations ... 
Remarks_here: 0x422dc hal_sfc_reg_flash_opreations 

Miss_funcptr: hal_dma_v151_irq_handler ... 
Remarks_here: 0x42aae hal_dma_v151_irq_handler 

Miss_funcptr: hal_dma_reg_ch_get ... 
Remarks_here: 0x42b34 hal_dma_reg_ch_get 

Miss_funcptr: hal_dma_reg_ch_set.constprop.0 ... 
Remarks_here: 0x42ba0 hal_dma_reg_ch_set.constprop.0 

Miss_funcptr: hal_uart_write ... 
Remarks_here: 0x43512 hal_uart_write 

Miss_funcptr: uart_error_isr ... 
Remarks_here: 0x42d86 uart_error_isr 

Miss_funcptr: hal_uart_ctrl ... 
Remarks_here: 0x43532 hal_uart_ctrl 

Miss_funcptr: hal_uart_read ... 
Remarks_here: 0x43522 hal_uart_read 

Miss_funcptr: uart_evt_callback ... 
Remarks_here: 0x42e04 uart_evt_callback 

Miss_funcptr: uart_evt_callback ... 
Remarks_here: 0x42e04 uart_evt_callback 

Miss_funcptr: uart_evt_callback ... 
Remarks_here: 0x42e04 uart_evt_callback 

Miss_funcptr: uart_evt_callback ... 
Remarks_here: 0x42e04 uart_evt_callback 

Miss_funcptr: uart_evt_callback ... 
Remarks_here: 0x42e04 uart_evt_callback 

Miss_funcptr: uapi_pin_set_mode ... 
Remarks_here: 0x90124942 uapi_pin_set_mode 

Miss_funcptr: uapi_pin_set_ie ... 
Remarks_here: 0x90124a34 uapi_pin_set_ie 

Miss_funcptr: hal_uart_init ... 
Remarks_here: 0x434c2 hal_uart_init 

Miss_funcptr: hal_uart_set_dma_config ... 
Remarks_here: 0x4354c hal_uart_set_dma_config 

Miss_funcptr: hal_uart_deinit ... 
Remarks_here: 0x43502 hal_uart_deinit 

Miss_funcptr: hal_uart_v151_ctrl ... 
Remarks_here: 0x4366c hal_uart_v151_ctrl 

Miss_funcptr: hal_uart_enable_interrupt ... 
Remarks_here: 0x43782 hal_uart_enable_interrupt 

Miss_funcptr: hal_uart_irq_handler ... 
Remarks_here: 0x43a44 hal_uart_irq_handler 

Miss_funcptr: hal_uart_irq_handler ... 
Remarks_here: 0x43a44 hal_uart_irq_handler 

Miss_funcptr: uapi_watchdog_resume ... 
Remarks_here: 0x90124fb2 uapi_watchdog_resume 

Miss_funcptr: uapi_watchdog_resume ... 
Remarks_here: 0x90124fb2 uapi_watchdog_resume 

Miss_funcptr: lowpower_cpu_resume ... 
Remarks_here: 0x446f4 lowpower_cpu_resume 

Miss_funcptr: uapi_pm_dev_suspend ... 
Remarks_here: 0x4481e uapi_pm_dev_suspend 

Miss_funcptr: uapi_pm_dev_resume ... 
Remarks_here: 0x44840 uapi_pm_dev_resume 

Miss_funcptr: uapi_pm_process_fsm_handler ... 
Remarks_here: 0x44862 uapi_pm_process_fsm_handler 

Miss_funcptr: pm_allow_deepsleep ... 
Remarks_here: 0x44952 pm_allow_deepsleep 

Miss_funcptr: pm_enter_wfi ... 
Remarks_here: 0x44960 pm_enter_wfi 

Miss_funcptr: pm_cpu_resume ... 
Remarks_here: 0x4496e pm_cpu_resume 

Miss_funcptr: uapi_pm_enter_sleep ... 
Remarks_here: 0x4497c uapi_pm_enter_sleep 

Miss_funcptr: uapi_pm_enter_sleep ... 
Remarks_here: 0x4497c uapi_pm_enter_sleep 

Miss_funcptr: uapi_pm_enter_sleep ... 
Remarks_here: 0x4497c uapi_pm_enter_sleep 

Miss_funcptr: OsIntEntry ... 
Remarks_here: 0x45800 OsIntEntry 

Miss_funcptr: OsIntHandle ... 
Remarks_here: 0x45792 OsIntHandle 

Miss_funcptr: OsIntHandle ... 
Remarks_here: 0x45792 OsIntHandle 

Miss_funcptr: OsIntHandle ... 
Remarks_here: 0x45792 OsIntHandle 

Miss_funcptr: OsSwtmrTask ... 
Remarks_here: 0x45fd6 OsSwtmrTask 

Miss_funcptr: OsIdleHandler ... 
Remarks_here: 0x46238 OsIdleHandler 

Miss_funcptr: OsTaskEntry ... 
Remarks_here: 0x46256 OsTaskEntry 

Miss_funcptr: evt_prog_finish_eeq_isr_patch ... 
Remarks_here: 0x47038 evt_prog_finish_eeq_isr_patch 

Miss_funcptr: es_process_cancel_cbk_patch ... 
Remarks_here: 0x4706e es_process_cancel_cbk_patch 

Miss_funcptr: dpc_fsm_msg_process_patch ... 
Remarks_here: 0x478e6 dpc_fsm_msg_process_patch 

Miss_funcptr: dts_osal_ble_isr ... 
Remarks_here: 0x49c44 dts_osal_ble_isr 

Miss_funcptr: dts_osal_gle_isr ... 
Remarks_here: 0x49c54 dts_osal_gle_isr 

Miss_funcptr: bt_tsensor_enable ... 
Remarks_here: 0x9012e614 bt_tsensor_enable 

Miss_funcptr: bt_tsensor_update_temp ... 
Remarks_here: 0x9013049a bt_tsensor_update_temp 

Miss_funcptr: cali_run_callback ... 
Remarks_here: 0x90131a16 cali_run_callback 

Miss_funcptr: ble_isr_ram ... 
Remarks_here: 0x4a102 ble_isr_ram 

Miss_funcptr: gle_isr_ram ... 
Remarks_here: 0x4a266 gle_isr_ram 

Miss_funcptr: gle_aa_get_cfm_cbk_proc ... 
Remarks_here: 0x9017098c gle_aa_get_cfm_cbk_proc 

Miss_funcptr: gle_manager_exter_cbk ... 
Remarks_here: 0x9016ef0e gle_manager_exter_cbk 

Miss_funcptr: gle_manager_exter_cbk ... 
Remarks_here: 0x9016ef0e gle_manager_exter_cbk 

Miss_funcptr: invoke_current_state_action ... 
Remarks_here: 0x9012d47c invoke_current_state_action 

Miss_funcptr: gle_sm_exter_cbk ... 
Remarks_here: 0x901769f0 gle_sm_exter_cbk 

Miss_funcptr: gle_sm_exter_cbk ... 
Remarks_here: 0x901769f0 gle_sm_exter_cbk 

Miss_funcptr: sle_multi_conn_client_init ... 
Remarks_here: 0x90124036 sle_multi_conn_client_init 

Miss_funcptr: uapi_watchdog_init ... 
Remarks_here: 0x90124e90 uapi_watchdog_init 

Miss_funcptr: uapi_watchdog_init ... 
Remarks_here: 0x90124e90 uapi_watchdog_init 

Miss_funcptr: uapi_watchdog_enable ... 
Remarks_here: 0x90124ef0 uapi_watchdog_enable 

Miss_funcptr: uapi_pmp_config ... 
Remarks_here: 0x90124a80 uapi_pmp_config 

Miss_funcptr: OsMain ... 
Remarks_here: 0x90124552 OsMain 

Miss_funcptr: uapi_pmu_control ... 
Remarks_here: 0x90126324 uapi_pmu_control 

Miss_funcptr: clocks_init ... 
Remarks_here: 0x901267e8 clocks_init 

Miss_funcptr: print_system_boot_status ... 
Remarks_here: 0x9012bda4 print_system_boot_status 

Miss_funcptr: at_malloc ... 
Remarks_here: 0x901295e2 at_malloc 

Miss_funcptr: zdiag_set_enable ... 
Remarks_here: 0x90129d22 zdiag_set_enable 

Miss_funcptr: upg_malloc ... 
Remarks_here: 0x9012df6e upg_malloc 

Miss_funcptr: upg_free ... 
Remarks_here: 0x9012df86 upg_free 

Miss_funcptr: uapi_efuse_init ... 
Remarks_here: 0x90124ff8 uapi_efuse_init 

Miss_funcptr: HAL_RF_Ioctl ... 
Remarks_here: 0x901864c8 HAL_RF_Ioctl 

Miss_funcptr: uapi_pin_set_pull ... 
Remarks_here: 0x901249e8 uapi_pin_set_pull 

Miss_funcptr: uapi_pin_set_ds ... 
Remarks_here: 0x9012499c uapi_pin_set_ds 

Miss_funcptr: trng_deinit.isra.2 ... 
Remarks_here: 0x90124af4 trng_deinit.isra.2 

Miss_funcptr: uapi_watchdog_disable ... 
Remarks_here: 0x90124f3c uapi_watchdog_disable 

Miss_funcptr: uapi_watchdog_kick ... 
Remarks_here: 0x90124f78 uapi_watchdog_kick 

Miss_funcptr: uapi_efuse_read_buffer ... 
Remarks_here: 0x90125018 uapi_efuse_read_buffer 

Miss_funcptr: efuse_write_with_check ... 
Remarks_here: 0x90125082 efuse_write_with_check 

Miss_funcptr: hal_cpu_trace_lock_trigger.isra.1.part.2 ... 
Remarks_here: 0x9012514c hal_cpu_trace_lock_trigger.isra.1.part.2 

Miss_funcptr: hal_gpio_v150_irq_handler ... 
Remarks_here: 0x90125782 hal_gpio_v150_irq_handler 

Miss_funcptr: hal_pmp_riscv31_regs_set_pmpxcfg ... 
Remarks_here: 0x90125b82 hal_pmp_riscv31_regs_set_pmpxcfg 

Miss_funcptr: exec_fault_handler ... 
Remarks_here: 0x901265b2 exec_fault_handler 

Miss_funcptr: system_boot_reason_print ... 
Remarks_here: 0x9012bc16 system_boot_reason_print 

Miss_funcptr: app_tasks_init ... 
Remarks_here: 0x90128a14 app_tasks_init 

Miss_funcptr: do_hard_fault_handler ... 
Remarks_here: 0x9012743a do_hard_fault_handler 

Miss_funcptr: diag_pkt_router_output ... 
Remarks_here: 0x9012989e diag_pkt_router_output 

Miss_funcptr: diag_service_data_proc ... 
Remarks_here: 0x90129e34 diag_service_data_proc 

Miss_funcptr: uapi_diag_report_sys_msg_common ... 
Remarks_here: 0x9012a34a uapi_diag_report_sys_msg_common 

Miss_funcptr: transmit_dst_item_process_timer ... 
Remarks_here: 0x9012ae42 transmit_dst_item_process_timer 

Miss_funcptr: sapi_ble_hci_general ... 
Remarks_here: 0x9016be4c sapi_ble_hci_general 

Miss_funcptr: btsdk_load_transport_layer ... 
Remarks_here: 0x9016715c btsdk_load_transport_layer 

Miss_funcptr: bt_adpll_start_tx ... 
Remarks_here: 0x901300c8 bt_adpll_start_tx 

Miss_funcptr: uapi_at_channel_data_recv ... 
Remarks_here: 0x901292ea uapi_at_channel_data_recv 

Miss_funcptr: at_free ... 
Remarks_here: 0x901295f2 at_free 

Miss_funcptr: at_msg_queue_write ... 
Remarks_here: 0x9012960e at_msg_queue_write 

Miss_funcptr: at_proc_cmd_handle ... 
Remarks_here: 0x90129448 at_proc_cmd_handle 

Miss_funcptr: at_proc_cmd_handle ... 
Remarks_here: 0x90129448 at_proc_cmd_handle 

Miss_funcptr: at_parse_para_arguments ... 
Remarks_here: 0x90129060 at_parse_para_arguments 

Miss_funcptr: at_parse_para_arguments ... 
Remarks_here: 0x90129060 at_parse_para_arguments 

Miss_funcptr: uapi_at_report ... 
Remarks_here: 0x90129558 uapi_at_report 

Miss_funcptr: at_msg_queue_create ... 
Remarks_here: 0x90129600 at_msg_queue_create 

Miss_funcptr: at_msg_queue_read ... 
Remarks_here: 0x9012961e at_msg_queue_read 

Miss_funcptr: at_yield ... 
Remarks_here: 0x9012962e at_yield 

Miss_funcptr: diag_pkt_router_run_cmd ... 
Remarks_here: 0x9012a112 diag_pkt_router_run_cmd 

Miss_funcptr: transmit_service_process ... 
Remarks_here: 0x9012a67e transmit_service_process 

Miss_funcptr: transmit_src_item_process_data_request_frame ... 
Remarks_here: 0x9012afa8 transmit_src_item_process_data_request_frame 

Miss_funcptr: transmit_dst_item_process_data_reply_frame ... 
Remarks_here: 0x9012ac76 transmit_dst_item_process_data_reply_frame 

Miss_funcptr: transmit_dst_item_process_data_reply_frame ... 
Remarks_here: 0x9012ac76 transmit_dst_item_process_data_reply_frame 

Miss_funcptr: transmit_item_process_notify_frame ... 
Remarks_here: 0x9012aa50 transmit_item_process_notify_frame 

Miss_funcptr: panic_deal ... 
Remarks_here: 0x9012b99a panic_deal 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: sm3_compress_blocks ... 
Remarks_here: 0x9018702a sm3_compress_blocks 

Miss_funcptr: pm_fsm_wakeup_handler ... 
Remarks_here: 0x9012e246 pm_fsm_wakeup_handler 

Miss_funcptr: lm_gle_acb_del_all_srv_fsm ... 
Remarks_here: 0x9014c296 lm_gle_acb_del_all_srv_fsm 

Miss_funcptr: lm_gle_iob_del_all_srv_fsm ... 
Remarks_here: 0x90153ce0 lm_gle_iob_del_all_srv_fsm 

Miss_funcptr: bt_common_msg_except_process ... 
Remarks_here: 0x90131224 bt_common_msg_except_process 

Miss_funcptr: encrypt_done_sm3 ... 
Remarks_here: 0x90131642 encrypt_done_sm3 

Miss_funcptr: hci_gle_rx_acb_data ... 
Remarks_here: 0x90131d6a hci_gle_rx_acb_data 

Miss_funcptr: hci_gle_rx_icb_data ... 
Remarks_here: 0x90131e2c hci_gle_rx_icb_data 

Miss_funcptr: api_c2h_write ... 
Remarks_here: 0x90131e42 api_c2h_write 

Miss_funcptr: evt_task_ble_acl_get_em_data.part.1 ... 
Remarks_here: 0x901336c8 evt_task_ble_acl_get_em_data.part.1 

Miss_funcptr: evt_task_ble_per_adv_set_data ... 
Remarks_here: 0x901342ae evt_task_ble_per_adv_set_data 

Miss_funcptr: evt_task_ble_per_adv_set_data ... 
Remarks_here: 0x901342ae evt_task_ble_per_adv_set_data 

Miss_funcptr: evt_task_ble_adv_cte_config_sed ... 
Remarks_here: 0x9013454e evt_task_ble_adv_cte_config_sed 

Miss_funcptr: evt_task_ble_adv_isr_cbk ... 
Remarks_here: 0x90134a1e evt_task_ble_adv_isr_cbk 

Miss_funcptr: evt_task_ble_per_sync_isr_cbk ... 
Remarks_here: 0x901356c0 evt_task_ble_per_sync_isr_cbk 

Miss_funcptr: evt_task_ble_test_mode_isr_cbk ... 
Remarks_here: 0x90136808 evt_task_ble_test_mode_isr_cbk 

Miss_funcptr: general_tx_tog_up_callback ... 
Remarks_here: 0x90185616 general_tx_tog_up_callback 

Miss_funcptr: general_rx_tog_up_callback ... 
Remarks_here: 0x901858b2 general_rx_tog_up_callback 

Miss_funcptr: evt_task_gle_acb_get_em_data.part.4 ... 
Remarks_here: 0x90138734 evt_task_gle_acb_get_em_data.part.4 

Miss_funcptr: extract_data_from_pointer ... 
Remarks_here: 0x901455b0 extract_data_from_pointer 

Miss_funcptr: extract_data_from_pointer ... 
Remarks_here: 0x901455b0 extract_data_from_pointer 

Miss_funcptr: set_wheel_to_zero ... 
Remarks_here: 0x901456de set_wheel_to_zero 

Miss_funcptr: evt_task_gle_acb_retry_isr_cbk ... 
Remarks_here: 0x90138f26 evt_task_gle_acb_retry_isr_cbk 

Miss_funcptr: evt_task_gle_acb_isr_cbk ... 
Remarks_here: 0x9013a006 evt_task_gle_acb_isr_cbk 

Miss_funcptr: evt_task_gle_acb_update_link_segment ... 
Remarks_here: 0x9013a05e evt_task_gle_acb_update_link_segment 

Miss_funcptr: evt_task_gle_cs_isr_cbk ... 
Remarks_here: 0x9013af28 evt_task_gle_cs_isr_cbk 

Miss_funcptr: evt_task_gle_t_cs_calcu_ts ... 
Remarks_here: 0x9013b180 evt_task_gle_t_cs_calcu_ts 

Miss_funcptr: evt_task_gle_cs_regs_init ... 
Remarks_here: 0x9013b3ea evt_task_gle_cs_regs_init 

Miss_funcptr: evt_task_gle_scan_isr_cbk ... 
Remarks_here: 0x9013cc28 evt_task_gle_scan_isr_cbk 

Miss_funcptr: evt_task_gle_initiate_isr_cbk ... 
Remarks_here: 0x9013e7ee evt_task_gle_initiate_isr_cbk 

Miss_funcptr: evt_task_gle_adv_evt_cbk ... 
Remarks_here: 0x9013fed4 evt_task_gle_adv_evt_cbk 

Miss_funcptr: evt_task_gle_adv_isr_cbk ... 
Remarks_here: 0x90140628 evt_task_gle_adv_isr_cbk 

Miss_funcptr: evt_task_gle_test_mode_isr_cbk ... 
Remarks_here: 0x90141754 evt_task_gle_test_mode_isr_cbk 

Miss_funcptr: imb_get_csccmpktcnt ... 
Remarks_here: 0x9014294a imb_get_csccmpktcnt 

Miss_funcptr: evt_task_gle_imb_isr_cbk ... 
Remarks_here: 0x90143aba evt_task_gle_imb_isr_cbk 

Miss_funcptr: evt_task_gle_iob_low_latency_set_reg ... 
Remarks_here: 0x9014410e evt_task_gle_iob_low_latency_set_reg 

Miss_funcptr: evt_task_gle_iob_low_latency_update_config ... 
Remarks_here: 0x90144940 evt_task_gle_iob_low_latency_update_config 

Miss_funcptr: evt_task_gle_iob_low_latency_set_ccm ... 
Remarks_here: 0x90144e02 evt_task_gle_iob_low_latency_set_ccm 

Miss_funcptr: evt_task_gle_iob_low_latency_isr_cbk ... 
Remarks_here: 0x90145500 evt_task_gle_iob_low_latency_isr_cbk 

Miss_funcptr: lm_gle_acb_fsm_exception_hci ... 
Remarks_here: 0x9014c0a8 lm_gle_acb_fsm_exception_hci 

Miss_funcptr: lm_gle_img_fsm_exception_hci ... 
Remarks_here: 0x90155372 lm_gle_img_fsm_exception_hci 

Miss_funcptr: dfx_config_samp_param ... 
Remarks_here: 0x90156260 dfx_config_samp_param 

Miss_funcptr: dfx_config_samp_param ... 
Remarks_here: 0x90156260 dfx_config_samp_param 

Miss_funcptr: dfx_config_samp_param ... 
Remarks_here: 0x90156260 dfx_config_samp_param 

Miss_funcptr: dfx_config_samp_param ... 
Remarks_here: 0x90156260 dfx_config_samp_param 

Miss_funcptr: dfx_config_samp_param ... 
Remarks_here: 0x90156260 dfx_config_samp_param 

Miss_funcptr: dfx_config_samp_param ... 
Remarks_here: 0x90156260 dfx_config_samp_param 

Miss_funcptr: dfx_bt_cmd_handler_table ... 
Remarks_here: 0x90156590 dfx_bt_cmd_handler_table 

Miss_funcptr: fsm_q_buf_done ... 
Remarks_here: 0x90156a02 fsm_q_buf_done 

Miss_funcptr: schedule_loop ... 
Remarks_here: 0x90156dea schedule_loop 

Miss_funcptr: fsm_direct_event ... 
Remarks_here: 0x901570a4 fsm_direct_event 

Miss_funcptr: timer_expire_in_sch_lite ... 
Remarks_here: 0x901570c8 timer_expire_in_sch_lite 

Miss_funcptr: timer_expire_in_sch_lite ... 
Remarks_here: 0x901570c8 timer_expire_in_sch_lite 

Miss_funcptr: btsrv_handle_task_msg ... 
Remarks_here: 0x90157762 btsrv_handle_task_msg 

Miss_funcptr: ble_at_link_common ... 
Remarks_here: 0x90159058 ble_at_link_common 

Miss_funcptr: ble_gatt_server_event_handle ... 
Remarks_here: 0x9015ffc4 ble_gatt_server_event_handle 

Miss_funcptr: ble_gatt_client_event_handle ... 
Remarks_here: 0x901601f8 ble_gatt_client_event_handle 

Miss_funcptr: test_ble_factory_vendor_pdl_cmd_complete ... 
Remarks_here: 0x90159bf8 test_ble_factory_vendor_pdl_cmd_complete 

Miss_funcptr: test_ble_factory_vendor_pdl_cmd_complete ... 
Remarks_here: 0x90159bf8 test_ble_factory_vendor_pdl_cmd_complete 

Miss_funcptr: test_ble_factory_vendor_pdl_cmd_complete ... 
Remarks_here: 0x90159bf8 test_ble_factory_vendor_pdl_cmd_complete 

Miss_funcptr: btsrv_handle_gatt_server_msg ... 
Remarks_here: 0x9015b406 btsrv_handle_gatt_server_msg 

Miss_funcptr: btsrv_handle_gatt_server_msg ... 
Remarks_here: 0x9015b406 btsrv_handle_gatt_server_msg 

Miss_funcptr: btsrv_handle_gatt_server_msg ... 
Remarks_here: 0x9015b406 btsrv_handle_gatt_server_msg 

Miss_funcptr: btsrv_handle_gatt_server_msg ... 
Remarks_here: 0x9015b406 btsrv_handle_gatt_server_msg 

Miss_funcptr: btsrv_handle_gatt_server_msg ... 
Remarks_here: 0x9015b406 btsrv_handle_gatt_server_msg 

Miss_funcptr: btsrv_handle_gatt_server_msg ... 
Remarks_here: 0x9015b406 btsrv_handle_gatt_server_msg 

Miss_funcptr: btsrv_handle_gatt_server_msg ... 
Remarks_here: 0x9015b406 btsrv_handle_gatt_server_msg 

Miss_funcptr: btsrv_handle_gatt_server_msg ... 
Remarks_here: 0x9015b406 btsrv_handle_gatt_server_msg 

Miss_funcptr: btsrv_handle_gatt_server_msg ... 
Remarks_here: 0x9015b406 btsrv_handle_gatt_server_msg 

Miss_funcptr: btsrv_handle_gatt_server_msg ... 
Remarks_here: 0x9015b406 btsrv_handle_gatt_server_msg 

Miss_funcptr: btsrv_handle_gatt_client_msg ... 
Remarks_here: 0x9015cbb0 btsrv_handle_gatt_client_msg 

Miss_funcptr: btsrv_handle_gatt_client_msg ... 
Remarks_here: 0x9015cbb0 btsrv_handle_gatt_client_msg 

Miss_funcptr: btsrv_handle_gatt_client_msg ... 
Remarks_here: 0x9015cbb0 btsrv_handle_gatt_client_msg 

Miss_funcptr: btsrv_handle_gatt_client_msg ... 
Remarks_here: 0x9015cbb0 btsrv_handle_gatt_client_msg 

Miss_funcptr: btsrv_handle_gatt_client_msg ... 
Remarks_here: 0x9015cbb0 btsrv_handle_gatt_client_msg 

Miss_funcptr: btsrv_handle_gatt_client_msg ... 
Remarks_here: 0x9015cbb0 btsrv_handle_gatt_client_msg 

Miss_funcptr: btsrv_default_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015dc40 btsrv_default_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_default_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015dc40 btsrv_default_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_default_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015dc40 btsrv_default_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_default_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015dc40 btsrv_default_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_default_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015dc40 btsrv_default_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_default_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015dc40 btsrv_default_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_default_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015dc40 btsrv_default_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_default_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015dc40 btsrv_default_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_default_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015dc40 btsrv_default_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_default_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015dc40 btsrv_default_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_default_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015dc40 btsrv_default_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_default_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015dc40 btsrv_default_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015de62 btsrv_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015de62 btsrv_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015de62 btsrv_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015de62 btsrv_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015de62 btsrv_handle_local_manager_msg_ble 

Miss_funcptr: btsrv_handle_local_manager_msg_ble ... 
Remarks_here: 0x9015de62 btsrv_handle_local_manager_msg_ble 

Miss_funcptr: bth_ota_chan_event_handler ... 
Remarks_here: 0x9015f834 bth_ota_chan_event_handler 

Miss_funcptr: bth_ota_chan_event_handler ... 
Remarks_here: 0x9015f834 bth_ota_chan_event_handler 

Miss_funcptr: btsrv_handle_low_latency_msg ... 
Remarks_here: 0x9015f8e0 btsrv_handle_low_latency_msg 

Miss_funcptr: ble_factory_evt_proc ... 
Remarks_here: 0x9015f8f8 ble_factory_evt_proc 

Miss_funcptr: btsrv_handle_hci_factory_msg ... 
Remarks_here: 0x9015fa2e btsrv_handle_hci_factory_msg 

Miss_funcptr: btsrv_handle_hci_factory_msg ... 
Remarks_here: 0x9015fa2e btsrv_handle_hci_factory_msg 

Miss_funcptr: btsrv_handle_hci_factory_msg ... 
Remarks_here: 0x9015fa2e btsrv_handle_hci_factory_msg 

Miss_funcptr: ble_l2cap_general_ind ... 
Remarks_here: 0x901603fa ble_l2cap_general_ind 

Miss_funcptr: ble_l2cap_internal_cbk ... 
Remarks_here: 0x901606ba ble_l2cap_internal_cbk 

Miss_funcptr: ble_hci_internal_cbk ... 
Remarks_here: 0x90160852 ble_hci_internal_cbk 

Miss_funcptr: hci_stream_to_struct_by_index ... 
Remarks_here: 0x90160a2e hci_stream_to_struct_by_index 

Miss_funcptr: hci_local_remove ... 
Remarks_here: 0x90162900 hci_local_remove 

Miss_funcptr: hcic_commandl2_input ... 
Remarks_here: 0x901618b2 hcic_commandl2_input 

Miss_funcptr: hcic_tl_reg ... 
Remarks_here: 0x90161934 hcic_tl_reg 

Miss_funcptr: bth_hci_acl_data_recv ... 
Remarks_here: 0x901638d6 bth_hci_acl_data_recv 

Miss_funcptr: bth_hci_acl_data_recombination ... 
Remarks_here: 0x901637d8 bth_hci_acl_data_recombination 

Miss_funcptr: hcic_local_rtx ... 
Remarks_here: 0x9016230e hcic_local_rtx 

Miss_funcptr: gaph_le_mgr_adv_set_terminated_ind_new ... 
Remarks_here: 0x9016563e gaph_le_mgr_adv_set_terminated_ind_new 

Miss_funcptr: gaph_le_mgr_ext_adv_report_ind ... 
Remarks_here: 0x9016573e gaph_le_mgr_ext_adv_report_ind 

Miss_funcptr: gaph_le_mgr_adv_report_ind ... 
Remarks_here: 0x9016583e gaph_le_mgr_adv_report_ind 

Miss_funcptr: hlp_vendor_event_ind ... 
Remarks_here: 0x90166a32 hlp_vendor_event_ind 

Miss_funcptr: hlp_call_upper ... 
Remarks_here: 0x90166a5c hlp_call_upper 

Miss_funcptr: hlp_call_upper ... 
Remarks_here: 0x90166a5c hlp_call_upper 

Miss_funcptr: hlp_call_upper ... 
Remarks_here: 0x90166a5c hlp_call_upper 

Miss_funcptr: hlp_call_upper ... 
Remarks_here: 0x90166a5c hlp_call_upper 

Miss_funcptr: hlp_call_upper ... 
Remarks_here: 0x90166a5c hlp_call_upper 

Miss_funcptr: hlp_call_upper ... 
Remarks_here: 0x90166a5c hlp_call_upper 

Miss_funcptr: btsdk_gatth_call_upper ... 
Remarks_here: 0x9016892c btsdk_gatth_call_upper 

Miss_funcptr: bt_sdk_gatt_task_complete_handle ... 
Remarks_here: 0x90168e7a bt_sdk_gatt_task_complete_handle 

Miss_funcptr: btsdk_gatth_callback ... 
Remarks_here: 0x90168ebe btsdk_gatth_callback 

Miss_funcptr: btsdk_gatth_server_reg_tree_cfm ... 
Remarks_here: 0x90169570 btsdk_gatth_server_reg_tree_cfm 

Miss_funcptr: btsdk_gatth_server_app_execute_write_cbk ... 
Remarks_here: 0x90169806 btsdk_gatth_server_app_execute_write_cbk 

Miss_funcptr: btsdk_gatth_server_read_ind ... 
Remarks_here: 0x90169834 btsdk_gatth_server_read_ind 

Miss_funcptr: btsdk_gatth_server_write_ind ... 
Remarks_here: 0x901698bc btsdk_gatth_server_write_ind 

Miss_funcptr: btsdk_gatth_send_response ... 
Remarks_here: 0x90169d5e btsdk_gatth_send_response 

Miss_funcptr: btsdk_gatth_server_callback ... 
Remarks_here: 0x9016a008 btsdk_gatth_server_callback 

Miss_funcptr: ble_gatt_client_call_back_func ... 
Remarks_here: 0x9016ad78 ble_gatt_client_call_back_func 

Miss_funcptr: ble_gatt_client_call_back_func ... 
Remarks_here: 0x9016ad78 ble_gatt_client_call_back_func 

Miss_funcptr: ble_general_l2cap_callback ... 
Remarks_here: 0x9016b8c0 ble_general_l2cap_callback 

Miss_funcptr: ble_general_hci_data_process.constprop.3 ... 
Remarks_here: 0x9016bdd6 ble_general_hci_data_process.constprop.3 

Miss_funcptr: app_ble_message_handle ... 
Remarks_here: 0x9016cd56 app_ble_message_handle 

Miss_funcptr: app_ble_message_handle ... 
Remarks_here: 0x9016cd56 app_ble_message_handle 

Miss_funcptr: gle_dm_trans_lcid_dispatch_tcids ... 
Remarks_here: 0x9016f2fc gle_dm_trans_lcid_dispatch_tcids 

Miss_funcptr: gle_hci_command_clear ... 
Remarks_here: 0x90172796 gle_hci_command_clear 

Miss_funcptr: gle_cm_exter_cbk ... 
Remarks_here: 0x9016d9be gle_cm_exter_cbk 

Miss_funcptr: gle_cm_exter_cbk ... 
Remarks_here: 0x9016d9be gle_cm_exter_cbk 

Miss_funcptr: cs_event_cbk ... 
Remarks_here: 0x9017b3f4 cs_event_cbk 

Miss_funcptr: cs_task_send ... 
Remarks_here: 0x9017b94a cs_task_send 

Miss_funcptr: gle_tm_exter_cbk ... 
Remarks_here: 0x90173b8c gle_tm_exter_cbk 

Miss_funcptr: gle_general_command_proc_dispatch_core ... 
Remarks_here: 0x9016eccc gle_general_command_proc_dispatch_core 

Miss_funcptr: gle_dd_exter_cbk ... 
Remarks_here: 0x901701bc gle_dd_exter_cbk 

Miss_funcptr: gle_dd_exter_cbk ... 
Remarks_here: 0x901701bc gle_dd_exter_cbk 

Miss_funcptr: gle_dd_exter_cbk ... 
Remarks_here: 0x901701bc gle_dd_exter_cbk 

Miss_funcptr: gle_hci_command_remove ... 
Remarks_here: 0x9017259a gle_hci_command_remove 

Miss_funcptr: gle_hci_cbk_traverse ... 
Remarks_here: 0x90172bec gle_hci_cbk_traverse 

Miss_funcptr: gle_hci_acb_data_recv ... 
Remarks_here: 0x90173152 gle_hci_acb_data_recv 

Miss_funcptr: gle_tm_data_recv_core ... 
Remarks_here: 0x90173a4a gle_tm_data_recv_core 

Miss_funcptr: tm_recv_data_ind_dispatch ... 
Remarks_here: 0x901746a8 tm_recv_data_ind_dispatch 

Miss_funcptr: gle_sm_data_recv_core ... 
Remarks_here: 0x901760ec gle_sm_data_recv_core 

Miss_funcptr: gle_tm_signal_capability_cfm ... 
Remarks_here: 0x9017411e gle_tm_signal_capability_cfm 

Miss_funcptr: gle_tm_signal_capability_rsp ... 
Remarks_here: 0x90174348 gle_tm_signal_capability_rsp 

Miss_funcptr: ssap_recv_data_ind_cbk ... 
Remarks_here: 0x901770aa ssap_recv_data_ind_cbk 

Miss_funcptr: ssap_recv_data_ind_cbk ... 
Remarks_here: 0x901770aa ssap_recv_data_ind_cbk 

Miss_funcptr: cs_pdu_tl_send ... 
Remarks_here: 0x9019416a cs_pdu_tl_send 

Miss_funcptr: ssaps_event_callback_handler ... 
Remarks_here: 0x90179f80 ssaps_event_callback_handler 

Miss_funcptr: ssaps_start_service_handle ... 
Remarks_here: 0x90179e3a ssaps_start_service_handle 

Miss_funcptr: ssaps_find_hdl_by_uuid_handle ... 
Remarks_here: 0x90179ebc ssaps_find_hdl_by_uuid_handle 

Miss_funcptr: ssaps_write_req_cbk_handle ... 
Remarks_here: 0x90179eda ssaps_write_req_cbk_handle 

Miss_funcptr: ssaps_read_req_cbk_handle ... 
Remarks_here: 0x90179ef8 ssaps_read_req_cbk_handle 

Miss_funcptr: ssaps_update_value_cbk_handle ... 
Remarks_here: 0x90179f16 ssaps_update_value_cbk_handle 

Miss_funcptr: ssaps_mtu_req_cbk_handle ... 
Remarks_here: 0x90179f34 ssaps_mtu_req_cbk_handle 

Miss_funcptr: ssaps_indicate_cfm_value_cbk_handle ... 
Remarks_here: 0x90179f5a ssaps_indicate_cfm_value_cbk_handle 

Miss_funcptr: ssapc_discovery_services_cfm ... 
Remarks_here: 0x90178f5a ssapc_discovery_services_cfm 

Miss_funcptr: ssapc_value_cbk_handle ... 
Remarks_here: 0x9017777c ssapc_value_cbk_handle 

Miss_funcptr: ssapc_write_req_task_send ... 
Remarks_here: 0x90177a2e ssapc_write_req_task_send 

Miss_funcptr: ssapc_task_event_callback ... 
Remarks_here: 0x90177b86 ssapc_task_event_callback 

Miss_funcptr: ssaps_add_service_fsm ... 
Remarks_here: 0x9017c700 ssaps_add_service_fsm 

Miss_funcptr: ssaps_add_property_fsm ... 
Remarks_here: 0x9017c758 ssaps_add_property_fsm 

Miss_funcptr: ssaps_add_desc_fsm ... 
Remarks_here: 0x9017c83e ssaps_add_desc_fsm 

Miss_funcptr: clear_service_list ... 
Remarks_here: 0x901820f8 clear_service_list 

Miss_funcptr: gle_mgr_general_evt_handle ... 
Remarks_here: 0x9017e924 gle_mgr_general_evt_handle 

Miss_funcptr: btsrv_handle_sle_connection_msg ... 
Remarks_here: 0x9017fc72 btsrv_handle_sle_connection_msg 

Miss_funcptr: btsrv_handle_sle_connection_msg ... 
Remarks_here: 0x9017fc72 btsrv_handle_sle_connection_msg 

Miss_funcptr: btsrv_handle_sle_connection_msg ... 
Remarks_here: 0x9017fc72 btsrv_handle_sle_connection_msg 

Miss_funcptr: btsrv_handle_sle_connection_msg ... 
Remarks_here: 0x9017fc72 btsrv_handle_sle_connection_msg 

Miss_funcptr: btsrv_handle_sle_connection_msg ... 
Remarks_here: 0x9017fc72 btsrv_handle_sle_connection_msg 

Miss_funcptr: btsrv_handle_sle_connection_msg ... 
Remarks_here: 0x9017fc72 btsrv_handle_sle_connection_msg 

Miss_funcptr: btsrv_handle_sle_connection_msg ... 
Remarks_here: 0x9017fc72 btsrv_handle_sle_connection_msg 

Miss_funcptr: sle_low_latency_set_em_data_cbk ... 
Remarks_here: 0x90184fc2 sle_low_latency_set_em_data_cbk 

Miss_funcptr: btsrv_sle_sm_passkey_req ... 
Remarks_here: 0x901809ec btsrv_sle_sm_passkey_req 

Miss_funcptr: btsrv_sle_sm_passkey_notify ... 
Remarks_here: 0x901809fa btsrv_sle_sm_passkey_notify 

Miss_funcptr: btsrv_sle_adv_cbk ... 
Remarks_here: 0x90180d78 btsrv_sle_adv_cbk 

Miss_funcptr: btsrv_handle_sle_device_manager_msg ... 
Remarks_here: 0x90180e2c btsrv_handle_sle_device_manager_msg 

Miss_funcptr: btsrv_handle_sle_device_discovery_msg ... 
Remarks_here: 0x90180fac btsrv_handle_sle_device_discovery_msg 

Miss_funcptr: btsrv_handle_sle_device_discovery_msg ... 
Remarks_here: 0x90180fac btsrv_handle_sle_device_discovery_msg 

Miss_funcptr: btsrv_handle_sle_device_discovery_msg ... 
Remarks_here: 0x90180fac btsrv_handle_sle_device_discovery_msg 

Miss_funcptr: btsrv_handle_sle_device_discovery_msg ... 
Remarks_here: 0x90180fac btsrv_handle_sle_device_discovery_msg 

Miss_funcptr: btsrv_handle_sle_device_discovery_msg ... 
Remarks_here: 0x90180fac btsrv_handle_sle_device_discovery_msg 

Miss_funcptr: btsrv_handle_ssaps_msg ... 
Remarks_here: 0x90183168 btsrv_handle_ssaps_msg 

Miss_funcptr: btsrv_handle_ssaps_msg ... 
Remarks_here: 0x90183168 btsrv_handle_ssaps_msg 

Miss_funcptr: btsrv_handle_ssaps_msg ... 
Remarks_here: 0x90183168 btsrv_handle_ssaps_msg 

Miss_funcptr: btsrv_handle_ssaps_msg ... 
Remarks_here: 0x90183168 btsrv_handle_ssaps_msg 

Miss_funcptr: btsrv_handle_ssaps_msg ... 
Remarks_here: 0x90183168 btsrv_handle_ssaps_msg 

Miss_funcptr: btsrv_handle_ssaps_msg ... 
Remarks_here: 0x90183168 btsrv_handle_ssaps_msg 

Miss_funcptr: gle_ssaps_delete_all_service_multi_cbk ... 
Remarks_here: 0x901848e8 gle_ssaps_delete_all_service_multi_cbk 

Miss_funcptr: gle_ssaps_start_service_multi_cbk ... 
Remarks_here: 0x901848a4 gle_ssaps_start_service_multi_cbk 

Miss_funcptr: gle_ssaps_mtu_changed_multi_cbk ... 
Remarks_here: 0x901849dc gle_ssaps_mtu_changed_multi_cbk 

Miss_funcptr: gle_ssaps_read_request_multi_cbk ... 
Remarks_here: 0x90184954 gle_ssaps_read_request_multi_cbk 

Miss_funcptr: gle_ssaps_read_by_uuid_request_multi_cbk ... 
Remarks_here: 0x90184976 gle_ssaps_read_by_uuid_request_multi_cbk 

Miss_funcptr: gle_ssaps_write_request_multi_cbk ... 
Remarks_here: 0x90184998 gle_ssaps_write_request_multi_cbk 

Miss_funcptr: gle_ssaps_indicate_cfm_multi_cbk ... 
Remarks_here: 0x901849ba gle_ssaps_indicate_cfm_multi_cbk 

Miss_funcptr: sle_ota_receive_data ... 
Remarks_here: 0x90185018 sle_ota_receive_data 

Miss_funcptr: btsrv_handle_sle_ssap_client_msg ... 
Remarks_here: 0x90183ce0 btsrv_handle_sle_ssap_client_msg 

Miss_funcptr: btsrv_handle_sle_ssap_client_msg ... 
Remarks_here: 0x90183ce0 btsrv_handle_sle_ssap_client_msg 

Miss_funcptr: ssapc_internal_value_handle_cbk ... 
Remarks_here: 0x9018405c ssapc_internal_value_handle_cbk 

Miss_funcptr: btsrv_handle_sle_glp_msg ... 
Remarks_here: 0x90184a0e btsrv_handle_sle_glp_msg 

Miss_funcptr: btsrv_handle_sle_factory_msg ... 
Remarks_here: 0x90184b28 btsrv_handle_sle_factory_msg 

Miss_funcptr: btsrv_handle_sle_transmission_msg ... 
Remarks_here: 0x90184eb4 btsrv_handle_sle_transmission_msg 

Miss_funcptr: tx_tog_up_callback ... 
Remarks_here: 0x90185150 tx_tog_up_callback 

Miss_funcptr: rx_tog_up_callback ... 
Remarks_here: 0x901854ba rx_tog_up_callback 

Miss_funcptr: NFC_InterruptDispatch ... 
Remarks_here: 0x9018651a NFC_InterruptDispatch 

Miss_funcptr: NFC_T2T_FieldOnProc ... 
Remarks_here: 0x9018698c NFC_T2T_FieldOnProc 

Miss_funcptr: NFC_T2T_TimeOutProc ... 
Remarks_here: 0x90186a6e NFC_T2T_TimeOutProc 

Miss_funcptr: NFC_T2T_TimeOutProc ... 
Remarks_here: 0x90186a6e NFC_T2T_TimeOutProc 
