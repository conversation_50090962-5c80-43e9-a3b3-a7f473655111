/**
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2025. All rights reserved.
 *
 * Description: SLE MULTI_CONN connection scheduler for 16 connections. \n
 *
 * History: \n
 * 2024-01-01, Create file for connection scheduling optimization. \n
 */

#include "sle_multi_conn_optimized.h"
#include "osal_debug.h"
#include "securec.h"

#define SLE_MULTI_CONN_SCHEDULER_LOG "[sle_scheduler]"
#define SLE_CONN_BATCH_SIZE 4  // 每批连接4个设备
#define SLE_CONN_INTERVAL_MS 100  // 连接间隔100ms

static sle_multi_conn_optimized_t g_optimized_conn_mgr = {0};
static uint8_t g_connection_priority[SLE_MULTI_CONN_MAX_CONNECTIONS] = {0};

/**
 * @brief 查找空闲连接槽
 */
uint8_t sle_multi_conn_find_free_slot(void)
{
    for (uint8_t i = 0; i < SLE_MULTI_CONN_MAX_CONNECTIONS; i++) {
        if (!sle_multi_conn_is_connected(i)) {
            return i;
        }
    }
    return SLE_MULTI_CONN_INVALID_INDEX;
}

/**
 * @brief 根据地址查找连接
 */
uint8_t sle_multi_conn_find_by_addr(const uint8_t *addr)
{
    if (addr == NULL) {
        return SLE_MULTI_CONN_INVALID_INDEX;
    }
    
    for (uint8_t i = 0; i < SLE_MULTI_CONN_MAX_CONNECTIONS; i++) {
        if (sle_multi_conn_is_connected(i) && 
            memcmp(addr, g_optimized_conn_mgr.connections[i].server_addr.addr, SLE_ADDR_LEN) == 0) {
            return i;
        }
    }
    return SLE_MULTI_CONN_INVALID_INDEX;
}

/**
 * @brief 根据连接ID查找连接
 */
uint8_t sle_multi_conn_find_by_conn_id(uint16_t conn_id)
{
    for (uint8_t i = 0; i < SLE_MULTI_CONN_MAX_CONNECTIONS; i++) {
        if (sle_multi_conn_is_connected(i) && 
            g_optimized_conn_mgr.connections[i].conn_id == conn_id) {
            return i;
        }
    }
    return SLE_MULTI_CONN_INVALID_INDEX;
}

/**
 * @brief 设置连接状态
 */
void sle_multi_conn_set_connected(uint8_t index, bool connected)
{
    if (index >= SLE_MULTI_CONN_MAX_CONNECTIONS) {
        return;
    }
    
    if (connected) {
        g_optimized_conn_mgr.status.connected_bitmap |= (1 << index);
        g_optimized_conn_mgr.status.connected_count++;
    } else {
        g_optimized_conn_mgr.status.connected_bitmap &= ~(1 << index);
        if (g_optimized_conn_mgr.status.connected_count > 0) {
            g_optimized_conn_mgr.status.connected_count--;
        }
    }
}

/**
 * @brief 检查连接状态
 */
bool sle_multi_conn_is_connected(uint8_t index)
{
    if (index >= SLE_MULTI_CONN_MAX_CONNECTIONS) {
        return false;
    }
    return (g_optimized_conn_mgr.status.connected_bitmap & (1 << index)) != 0;
}

/**
 * @brief 设置参数更新状态
 */
void sle_multi_conn_set_param_updated(uint8_t index, bool updated)
{
    if (index >= SLE_MULTI_CONN_MAX_CONNECTIONS) {
        return;
    }
    
    if (updated) {
        g_optimized_conn_mgr.status.param_update_bitmap |= (1 << index);
    } else {
        g_optimized_conn_mgr.status.param_update_bitmap &= ~(1 << index);
    }
}

/**
 * @brief 检查参数更新状态
 */
bool sle_multi_conn_is_param_updated(uint8_t index)
{
    if (index >= SLE_MULTI_CONN_MAX_CONNECTIONS) {
        return false;
    }
    return (g_optimized_conn_mgr.status.param_update_bitmap & (1 << index)) != 0;
}

/**
 * @brief 批量连接设备
 */
errcode_t sle_multi_conn_batch_connect(uint8_t start_index, uint8_t count)
{
    if (start_index >= SLE_MULTI_CONN_MAX_CONNECTIONS || count == 0) {
        return ERRCODE_INVALID_PARAM;
    }
    
    uint8_t end_index = start_index + count;
    if (end_index > SLE_MULTI_CONN_MAX_CONNECTIONS) {
        end_index = SLE_MULTI_CONN_MAX_CONNECTIONS;
    }
    
    osal_printk("%s batch connect from %d to %d\r\n", 
                SLE_MULTI_CONN_SCHEDULER_LOG, start_index, end_index - 1);
    
    for (uint8_t i = start_index; i < end_index; i++) {
        if (!sle_multi_conn_is_connected(i)) {
            // 启动连接到第i个服务器
            // 这里需要调用实际的连接函数
            osal_printk("%s connecting to server %d\r\n", SLE_MULTI_CONN_SCHEDULER_LOG, i);
            
            // 添加连接间隔，避免同时发起太多连接
            osal_msleep(SLE_CONN_INTERVAL_MS);
        }
    }
    
    return ERRCODE_SUCC;
}

/**
 * @brief 按优先级连接设备
 */
errcode_t sle_multi_conn_priority_connect(uint8_t *priority_list, uint8_t count)
{
    if (priority_list == NULL || count == 0) {
        return ERRCODE_INVALID_PARAM;
    }
    
    osal_printk("%s priority connect %d devices\r\n", SLE_MULTI_CONN_SCHEDULER_LOG, count);
    
    for (uint8_t i = 0; i < count && i < SLE_MULTI_CONN_MAX_CONNECTIONS; i++) {
        uint8_t server_index = priority_list[i];
        if (server_index < SLE_MULTI_CONN_MAX_CONNECTIONS && 
            !sle_multi_conn_is_connected(server_index)) {
            
            osal_printk("%s priority connecting to server %d\r\n", 
                        SLE_MULTI_CONN_SCHEDULER_LOG, server_index);
            
            // 这里需要调用实际的连接函数
            osal_msleep(SLE_CONN_INTERVAL_MS);
        }
    }
    
    return ERRCODE_SUCC;
}

/**
 * @brief 负载均衡扫描
 */
void sle_multi_conn_load_balance_scan(void)
{
    // 轮询扫描未连接的设备
    uint8_t scan_count = 0;
    uint8_t start_index = g_optimized_conn_mgr.status.scanning_index;
    
    for (uint8_t i = 0; i < SLE_MULTI_CONN_MAX_CONNECTIONS && scan_count < SLE_CONN_BATCH_SIZE; i++) {
        uint8_t index = (start_index + i) % SLE_MULTI_CONN_MAX_CONNECTIONS;
        
        if (!sle_multi_conn_is_connected(index)) {
            osal_printk("%s load balance scan server %d\r\n", SLE_MULTI_CONN_SCHEDULER_LOG, index);
            scan_count++;
        }
    }
    
    // 更新下次扫描起始位置
    g_optimized_conn_mgr.status.scanning_index = 
        (start_index + SLE_CONN_BATCH_SIZE) % SLE_MULTI_CONN_MAX_CONNECTIONS;
}

/**
 * @brief 获取连接统计信息
 */
void sle_multi_conn_get_statistics(void)
{
    osal_printk("%s === Connection Statistics ===\r\n", SLE_MULTI_CONN_SCHEDULER_LOG);
    osal_printk("%s Connected devices: %d/%d\r\n", 
                SLE_MULTI_CONN_SCHEDULER_LOG, 
                g_optimized_conn_mgr.status.connected_count, 
                SLE_MULTI_CONN_MAX_CONNECTIONS);
    osal_printk("%s Connected bitmap: 0x%04X\r\n", 
                SLE_MULTI_CONN_SCHEDULER_LOG, 
                g_optimized_conn_mgr.status.connected_bitmap);
    osal_printk("%s Param update bitmap: 0x%04X\r\n", 
                SLE_MULTI_CONN_SCHEDULER_LOG, 
                g_optimized_conn_mgr.status.param_update_bitmap);
}
