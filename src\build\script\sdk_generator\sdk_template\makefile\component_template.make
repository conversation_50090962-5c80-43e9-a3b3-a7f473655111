SRC_REPLACE_COMPONENT_NAME=REPLACE_SOURCES

C_OBJ_REPLACE_COMPONENT_NAME = $(subst $(SDK_ROOT),$(OUTPUT_DIR),$(patsubst %,%.obj,$(filter %.c,$(SRC_REPLACE_COMPONENT_NAME))))
CPP_OBJ_REPLACE_COMPONENT_NAME = $(subst $(SDK_ROOT),$(OUTPUT_DIR),$(patsubst %,%.obj,$(filter %.cpp,$(SRC_REPLACE_COMPONENT_NAME))))
CPP_OBJ_REPLACE_COMPONENT_NAME += $(subst $(SDK_ROOT),$(OUTPUT_DIR),$(patsubst %,%.obj,$(filter %.cc,$(SRC_REPLACE_COMPONENT_NAME))))
S_OBJ_REPLACE_COMPONENT_NAME = $(subst $(SDK_ROOT),$(OUTPUT_DIR),$(patsubst %,%.obj,$(filter %.S,$(SRC_REPLACE_COMPONENT_NAME))))
s_OBJ_REPLACE_COMPONENT_NAME = $(subst $(SDK_ROOT),$(OUTPUT_DIR),$(patsubst %,%.obj,$(filter %.s,$(SRC_REPLACE_COMPONENT_NAME))))
ALL_OBJ_REPLACE_COMPONENT_NAME = $(subst $(SDK_ROOT),$(OUTPUT_DIR),$(patsubst %,%.obj,$(SRC_REPLACE_COMPONENT_NAME)))

INCLUDES_TEMP_REPLACE_COMPONENT_NAME = REPLACE_PRIVATE_INCLUDE
INCLUDES_REPLACE_COMPONENT_NAME = $(patsubst %,-I%,$(INCLUDES_TEMP_REPLACE_COMPONENT_NAME))
INCLUDES_REPLACE_COMPONENT_NAME += $(PUBLIC_INCLUDES)

CCFLAGS_REPLACE_COMPONENT_NAME = $(PUBLIC_CCFLAGS)
CCFLAGS_REPLACE_COMPONENT_NAME += REPLACE_PRIVATE_CCFLAGS

DEFINES_REPLACE_COMPONENT_NAME = $(PUBLIC_DEFINES)
DEFINES_TEMP_REPLACE_COMPONENT_NAME = REPLACE_PRIVATE_DEFINES
DEFINES_REPLACE_COMPONENT_NAME += $(patsubst %,-D%,$(DEFINES_TEMP_REPLACE_COMPONENT_NAME))

LIBS_REPLACE_COMPONENT_NAME = REPLACE_LIBS
WHOLE_LINK_REPLACE_COMPONENT_NAME = REPLACE_WHOLE_LINK
ifeq ("$(WHOLE_LINK_REPLACE_COMPONENT_NAME)", "true")
    WHOLE_LINK_LIBS += REPLACE_COMPONENT_NAME
    WHOLE_EXTERN_LINK_LIBS += $(LIBS_REPLACE_COMPONENT_NAME)
else
    NORMAL_LINK_LIBS += REPLACE_COMPONENT_NAME
    NORMAL_EXTERN_LINK_LIBS += $(LIBS_REPLACE_COMPONENT_NAME)
endif

COMPONENT_NAME_REPLACE_COMPONENT_NAME=REPLACE_COMPONENT_NAME

LIB_DIR_TEMP_REPLACE_COMPONENT_NAME = REPLACE_LIB_DIR

LIB_DIR_REPLACE_COMPONENT_NAME = $(subst $(SDK_ROOT),$(OUTPUT_DIR),$(LIB_DIR_TEMP_REPLACE_COMPONENT_NAME))

LIB_DIR += $(LIB_DIR_REPLACE_COMPONENT_NAME)

LIB_EXIST := $(shell if [ -e "$(LIB_DIR_TEMP_REPLACE_COMPONENT_NAME)/lib$(COMPONENT_NAME_REPLACE_COMPONENT_NAME).a" ]; then echo "exist"; else echo "noexist"; fi )

MODULE_NAME_TEMP_REPLACE_COMPONENT_NAME = REPLACE_MODULE_NAME
AUTO_DEF_TEMP_REPLACE_COMPONENT_NAME = REPLACE_AUTO_DEF

lib$(COMPONENT_NAME_REPLACE_COMPONENT_NAME).a:$(ALL_OBJ_REPLACE_COMPONENT_NAME) HSO_DB_$(COMPONENT_NAME_REPLACE_COMPONENT_NAME)
	@mkdir -p $(LIB_DIR_REPLACE_COMPONENT_NAME)
	@$(RM) $(LIB_DIR_REPLACE_COMPONENT_NAME)/lib$(COMPONENT_NAME_REPLACE_COMPONENT_NAME).a
ifeq ("$(LIB_EXIST)", "noexist")
	@echo building $(LIB_DIR_REPLACE_COMPONENT_NAME)/lib$(COMPONENT_NAME_REPLACE_COMPONENT_NAME).a
	@$(AR) -rc $(LIB_DIR_REPLACE_COMPONENT_NAME)/lib$(COMPONENT_NAME_REPLACE_COMPONENT_NAME).a $(ALL_OBJ_REPLACE_COMPONENT_NAME)
else
	@echo copy $(LIB_DIR_REPLACE_COMPONENT_NAME)/lib$(COMPONENT_NAME_REPLACE_COMPONENT_NAME).a
	@cp $(LIB_DIR_TEMP_REPLACE_COMPONENT_NAME)/lib$(COMPONENT_NAME_REPLACE_COMPONENT_NAME).a $(LIB_DIR_REPLACE_COMPONENT_NAME)
endif

$(C_OBJ_REPLACE_COMPONENT_NAME): $(OUTPUT_DIR)/%.obj : $(SDK_ROOT)/%
	@echo Building $<
	@mkdir -p $(dir $@)
	@${CCACHE} $(CC) -c $(INCLUDES_REPLACE_COMPONENT_NAME) $(DEFINES_REPLACE_COMPONENT_NAME) $(CCFLAGS_REPLACE_COMPONENT_NAME) -DTHIS_FILE_ID=$(shell echo $(patsubst %.c, %_c, $(notdir  $<)) | tr '[a-z]' '[A-Z]') -DTHIS_MOD_ID=$(shell echo LOG_$(MODULE_NAME_TEMP_REPLACE_COMPONENT_NAME)MODULE | tr '[a-z]' '[A-Z]') $< -o $@;

$(CPP_OBJ_REPLACE_COMPONENT_NAME): $(OUTPUT_DIR)/%.obj : $(SDK_ROOT)/%
	@echo Building $<
	@mkdir -p $(dir $@)
	@${CCACHE} $(CXX) -c $(INCLUDES_REPLACE_COMPONENT_NAME) $(DEFINES_REPLACE_COMPONENT_NAME) $(CCFLAGS_REPLACE_COMPONENT_NAME) -DTHIS_FILE_ID=$(shell echo $(patsubst %.cpp, %_cpp, $(notdir  $<)) | tr '[a-z]' '[A-Z]') -DTHIS_MOD_ID=$(shell echo LOG_$(MODULE_NAME_TEMP_REPLACE_COMPONENT_NAME)MODULE | tr '[a-z]' '[A-Z]') $< -o $@;

$(s_OBJ_REPLACE_COMPONENT_NAME): $(OUTPUT_DIR)/%.obj : $(SDK_ROOT)/%
	@echo Building $<
	@mkdir -p $(dir $@)
	@$(AS) -c $(INCLUDES_REPLACE_COMPONENT_NAME) $(DEFINES_REPLACE_COMPONENT_NAME) $(CCFLAGS_REPLACE_COMPONENT_NAME) -DTHIS_FILE_ID=$(shell echo $(patsubst %.c, %_c, $(notdir  $<)) | tr '[a-z]' '[A-Z]') -DTHIS_MOD_ID=$(shell echo LOG_$(MODULE_NAME_TEMP_REPLACE_COMPONENT_NAME)MODULE | tr '[a-z]' '[A-Z]') $< -o $@;

$(S_OBJ_REPLACE_COMPONENT_NAME): $(OUTPUT_DIR)/%.obj : $(SDK_ROOT)/%
	@echo Building $<
	@mkdir -p $(dir $@)
	@$(AS) -c $(INCLUDES_REPLACE_COMPONENT_NAME) $(DEFINES_REPLACE_COMPONENT_NAME) $(CCFLAGS_REPLACE_COMPONENT_NAME) -DTHIS_FILE_ID=$(shell echo $(patsubst %.c, %_c, $(notdir  $<)) | tr '[a-z]' '[A-Z]') -DTHIS_MOD_ID=$(shell echo LOG_$(MODULE_NAME_TEMP_REPLACE_COMPONENT_NAME)MODULE | tr '[a-z]' '[A-Z]') $< -o $@;

HSO_DB_$(COMPONENT_NAME_REPLACE_COMPONENT_NAME):
ifneq ("$(BIN_NAME).bin", "ssb.bin")
ifneq ("$(words $(SRC_REPLACE_COMPONENT_NAME))", "0")
ifneq ($(nhso), true)
ifeq ("$(LIB_EXIST)", "noexist")
	@echo building $(COMPONENT_NAME_REPLACE_COMPONENT_NAME) HSO DB
	@echo -n $(LOG_DEF_LIST) | sed 's/ /,/g' > $(HSO_TMP)/$(COMPONENT_NAME_REPLACE_COMPONENT_NAME).txt
	@echo -n "####" >> $(HSO_TMP)/$(COMPONENT_NAME_REPLACE_COMPONENT_NAME).txt
	@echo -n $(SRC_REPLACE_COMPONENT_NAME) | sed 's/ /,/g' >> $(HSO_TMP)/$(COMPONENT_NAME_REPLACE_COMPONENT_NAME).txt
	@python3 $(HSO_MK_XML_PY) ${SDK_ROOT}/ ${CHIP} ${CORE} ${ARCH} ${AUTO_DEF_TEMP_REPLACE_COMPONENT_NAME} ${MODULE_NAME_TEMP_REPLACE_COMPONENT_NAME} FALSE $(HSO_TMP)/$(COMPONENT_NAME_REPLACE_COMPONENT_NAME).txt
endif
endif
else
	@echo "skip building $(COMPONENT_NAME_REPLACE_COMPONENT_NAME) HSO DB"
endif
endif
include ./toolchains.make
