#===============================================================================
# @brief    cmake file
# Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
#===============================================================================
add_subdirectory_if_exist(ate)
add_subdirectory_if_exist(ble_mouse)
add_subdirectory_if_exist(flashboot_linker)
add_subdirectory_if_exist(slem)
add_subdirectory_if_exist(loaderboot_linker)
add_subdirectory_if_exist(multi_mouse)
add_subdirectory_if_exist(romboot_linker)
add_subdirectory_if_exist(sle_dongle)
add_subdirectory_if_exist(standard)
add_subdirectory_if_exist(standard_tag)
add_subdirectory_if_exist(sec_boot)
add_subdirectory_if_exist(standard_mini)
add_subdirectory_if_exist(turnkey_mouse)