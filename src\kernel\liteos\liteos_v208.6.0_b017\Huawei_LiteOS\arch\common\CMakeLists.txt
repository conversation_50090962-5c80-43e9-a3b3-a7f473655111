set(<PERSON><PERSON><PERSON><PERSON>_NAME "archcommon")

# add global include, compile macro and options to public target #
set(MODULE_INCLUDE_PUB)
if(LOSCFG_ARCH_MMU_ENABLE)
list(APPEND MODULE_INCLUDE_PUB ${LITEOSTOPDIR}/arch/common/mmu/include)
endif()
set(MODULE_CXXINCLUDE_PUB)
set(MODULE_COPTS_PUB)
set(MODULE_ASOPTS_PUB)
set(MODULE_CXXOPTS_PUB)
set(MODULE_CMACRO_PUB)
set(MODULE_ASMACRO_PUB)
set(MODULE_CXXMACRO_PUB)


# add local include, compile macro and options to private target #
set(MODULE_INCLUDE_PRI )
set(MODULE_COPTS_PRI ${LITEOS_NON_SECURE_LOCAL_OPTS})
set(MODULE_ASOPTS_PRI ${LITEOS_NON_SECURE_LOCAL_OPTS})
set(MODU<PERSON>_CXXOPTS_PRI ${LITE<PERSON>_NON_SECURE_LOCAL_OPTS})
set(MODU<PERSON>_CMACRO_PRI)
set(MODULE_ASMACRO_PRI)
set(MODULE_CXXMACRO_PRI)


# add srcs to private target #
set(LOCAL_DIRS_y)
list(APPEND LOCAL_DIRS_${LOSCFG_ARCH_MMU_ENABLE} mmu/src)
FOREACH(CUR_DIR ${LOCAL_DIRS_y})
    aux_source_directory(${CUR_DIR} LOCAL_SRCS_y)
ENDFOREACH(CUR_DIR)

set(LOCAL_SRCS ${LOCAL_SRCS_y})
include(${MODULE})