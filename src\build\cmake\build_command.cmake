set(<PERSON>HO   ${CMAKE_COMMAND} -E echo)
set(<PERSON><PERSON><PERSON><PERSON>  ${CMAKE_COMMAND} -E make_directory)
set(RENAME  ${CMAKE_COMMAND} -E rename)
set(CP_PY  ${Python3_EXECUTABLE} ${BUILD_UTILS} copy)
set(CP_PY_FORCE  ${Python3_EXECUTABLE} ${BUILD_UTILS} copy_force)
set(CP_DIR ${CMAKE_COMMAND} -E copy_directory)
set(RM     ${CMAKE_COMMAND} -E rm -f)
set(RM_DIR ${CMAKE_COMMAND} -E rm -rf)
set(UNZIP ${CMAKE_COMMAND} -E tar x)

if(${BUILD_PLATFORM} MATCHES "linux")
set(CP     cp)
elseif(${BUILD_PLATFORM} MATCHES "windows")
set(CP     ${CMAKE_COMMAND} -E copy)
endif()