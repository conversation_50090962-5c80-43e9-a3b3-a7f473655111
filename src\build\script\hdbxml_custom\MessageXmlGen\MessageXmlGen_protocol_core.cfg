# The format of the configuration file is:
# <filename>, <regular expression - signals matching the regex are EXCLUDED from the generated file>
#  If the regular expression is blank, NO filtering will be performed.
#
# Lines starting # are not processed.


# Default for INTERNAL use only - NO messages are removed.
soc,protocol_core_messages.xml,

# Stack messages - No stack ID to log ID conversions are performed for "stack"
stack,stack.xml,

# Customer facing file - REMOVE all DSP and RF related events.
customer,protocol_core_messages_external.xml,DSP|LL1_RF|LL1_WARNING_RF|LL1_LOG_RF|FSM|DUMP_PDU|LL1_LOG_DEBUG_VALUE|LPM_VETO|VALID_SUBFRAMES|SCHEDULER_SLEEP|TIME_CONVERSION|MEM_STATS|NPDCCH_CANCELLED|LL1_SLEEP_TIMER_EXPIRY_IND|FREQ_ERROR|LL1_READ_NVCONFIG_DATA

