#===============================================================================
# @brief    cmake global variable init
# Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
#===============================================================================

set(ALL_PUBLIC_HEADER "" CACHE INTERNAL "" FORCE)
set(ALL_HEADER "" CACHE INTERNAL "" FORCE)
set(ALL_PUBLIC_DEFINES ${DEFINES} CACHE INTERNAL "" FORCE)
set(ALL_PUBLIC_CCFLAGS "${CCFLAGS}" CACHE INTERNAL "" FORCE)
set(ALL_SOURCES "" CACHE INTERNAL "" FORCE)
set(LOS_LIB "" CACHE INTERNAL "" FORCE)
set(LINKER_DIR "" CACHE INTERNAL "" FORCE)
set(SDK_PROJECT_FILE_DIR  "" CACHE INTERNAL "" FORCE)
set(LOG_DEF_LIST  "" CACHE INTERNAL "" FORCE)
set(WSTP_HEADER_LIST  "" CACHE INTERNAL "" FORCE)
set(PLAT_SRC_LIST  "" CACHE INTERNAL "" FORCE)
set(WSTP_SRC_LIST  "" CACHE INTERNAL "" FORCE)
set(TEST_HAED_LIST  "" CACHE INTERNAL "" FORCE)
set(BTH_SDK_LIST  "" CACHE INTERNAL "" FORCE)