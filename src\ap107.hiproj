[information]
series_name=cfbb
board=bs21e
sdk_path=d:\2025BLE\sle\fbb_bs2x_ap\src
flash=undefined
board_build.mcu=bs21e
platform=cfbb
json_path=bs21e.json
project_type=CFBB
project_new_type=commonProject
target=STANDARD-BS21E-N1100E
check_empty_type=

[chipconfig]
chipconfig=false

[variabletrace]
variabletrace=false

[compile]
tool_chain=cc_riscv32_musl_fp_win
link_c_library_in_toolchain=yes
link_c_library_in_compilationchain=yes
custom_build_command=standard-bs21e-1100e
custom_clean_command=undefined
map_path=./output/bs21e/acore/standard-bs21e-1100e/application.map
compile_type=debug
constant_type=float
optimization=O0
warning=yes
werror=no
wno_unused_function=no
wno_unused_label=no
wno_unused_parameter=no
wno_unused_variable=no
wno_missing_prototypes=no
werr_implicit_func=yes
static_library_enable=no
static_library_name=
static_library_dependency_header_file=
static_library_source_file=
fstack_protector_strong=no
extern_staticlib_path=
extern_staticlib_include=
global_macro_definition=
generate_crc=no
generate_checksum=no
generate_symboltable=yes
generate_target_hex=yes
parse_elf_for_livewatch=no
enable_perf=no
parse_analysis_json=yes
padding=no

[debug]
elf_path=./output/bs21e/acore/standard-bs21e-1100e/application.elf
breakpoints_limitation=7
client=gdb
tool=jlink
interface=swd
speed=5000
openocd_interface_file=
openocd_target_file=
timeout=60000
jlinkScriptPath=f:\data\SLE\HiSpark\resources\app\extensions\huawei.hisparkprojectwizard-0.0.4\resources\connect\CFBB\bs21e\connectCore.JLinkScript
stop_debug_state=restart

[upload]
bin_path=./tools/pkg/fwpkg/bs21e/bs21e_all.fwpkg
protocol=serial
reset=1
burn_verification=0
usb_value=
pid_value=
vid_value=
usage=
usage_page=
port=
baud=750000
stop_bit=0
parity=N
inside_protocol=

[console]
serial_port=
baud=115200
stop_bit=0
parity=N

[analysis]
elf_path=./output/bs21e/acore/standard-bs21e-1100e/application.elf
map_path=./output/bs21e/acore/standard-bs21e-1100e/application.map
tool_path=./tools/bin/compiler/riscv/cc_riscv32_musl_b010/cc_riscv32_musl_fp_win/bin

[kConfig]
menu_config_file_path=config.in
menu_config_build_target=standard-bs21e-1100e
menu_config_core=acore
menu_config_target_path=./build/config/target_config/bs21e/menuconfig/acore
